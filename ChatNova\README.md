# ChatNova - Next-Gen AI Companion

A visually stunning, highly interactive chatbot website powered by Google Gemini AI, featuring advanced PDF processing, voice interaction, and an empathetic virtual companion experience.

![Chat<PERSON>ova](https://img.shields.io/badge/ChatNova-v1.0.0-blue.svg)
![Next.js](https://img.shields.io/badge/Next.js-14.0.4-black.svg)
![TypeScript](https://img.shields.io/badge/TypeScript-5.0-blue.svg)
![Tailwind CSS](https://img.shields.io/badge/Tailwind-3.3.0-38B2AC.svg)

## ✨ Features

### 🤖 Intelligent AI Companion
- **Powered by Google Gemini AI** for natural, contextual conversations
- **Empathetic personality** with emotional intelligence and adaptive responses
- **Customizable chatbot avatar** with dynamic expressions and animations
- **Multi-personality support** with different tones and characteristics

### 📄 Advanced PDF Processing
- **Drag & drop PDF upload** with elegant file handling
- **Intelligent text extraction** using PDF.js
- **Smart content analysis** and summarization
- **Searchable document insights** with keyword extraction
- **Multi-file support** for comprehensive document analysis

### 🎤 Voice Interaction
- **Real-time speech recognition** with Web Speech API
- **Natural voice responses** with text-to-speech
- **Voice command support** for hands-free interaction
- **Multi-language support** for global accessibility

### 🎨 Stunning Visual Design
- **Futuristic UI/UX** with smooth animations and transitions
- **Glass morphism effects** and gradient backgrounds
- **Dark/Light mode toggle** with system preference detection
- **Responsive design** optimized for all devices
- **Neural network background** with floating particles

### 🚀 Performance & Accessibility
- **Lightning-fast responses** with optimized API calls
- **Real-time typing indicators** and message animations
- **Full accessibility support** with screen reader compatibility
- **Keyboard navigation** and ARIA labels
- **Progressive Web App** capabilities

## 🛠️ Technology Stack

- **Frontend**: Next.js 14, React 18, TypeScript
- **Styling**: Tailwind CSS, Framer Motion, Radix UI
- **AI Integration**: Google Gemini API
- **PDF Processing**: PDF.js
- **State Management**: Zustand
- **Voice Features**: Web Speech API
- **File Handling**: React Dropzone

## 🚀 Quick Start

### Prerequisites

- Node.js 18+ and npm/yarn
- Google Gemini API key ([Get one here](https://makersuite.google.com/app/apikey))

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/yourusername/chatnova.git
   cd chatnova
   ```

2. **Install dependencies**
   ```bash
   npm install
   # or
   yarn install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.local.example .env.local
   ```
   
   Edit `.env.local` and add your Gemini API key:
   ```env
   GEMINI_API_KEY=your_gemini_api_key_here
   ```

4. **Run the development server**
   ```bash
   npm run dev
   # or
   yarn dev
   ```

5. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## 📖 Usage Guide

### Getting Started
1. **Welcome Screen**: First-time visitors see an interactive welcome screen
2. **Start Chatting**: Click "Start Chatting" to begin your AI conversation
3. **Upload PDFs**: Use the paperclip icon or drag & drop to upload documents
4. **Voice Input**: Enable voice mode in settings for hands-free interaction

### PDF Analysis
1. **Upload**: Drag PDF files to the upload zone or click to browse
2. **Processing**: Watch real-time extraction progress
3. **Analysis**: Ask questions about your documents or get automatic insights
4. **Multi-file**: Upload multiple PDFs for comprehensive analysis

### Customization
- **Theme**: Toggle between dark and light modes
- **Voice**: Enable/disable voice features
- **Personality**: Customize AI companion traits (coming soon)
- **Language**: Multi-language support (coming soon)

## 🔧 Configuration

### Environment Variables

```env
# Required
GEMINI_API_KEY=your_gemini_api_key_here

# Optional
NEXT_PUBLIC_APP_NAME=ChatNova
NEXT_PUBLIC_APP_VERSION=1.0.0
```

### Customization Options

#### AI Personality
Edit `stores/chat-store.ts` to customize the default personality:

```typescript
const defaultPersonality: ChatbotPersonality = {
  name: 'Nova',
  tone: 'empathetic',
  avatar: '🤖',
  description: 'Your intelligent and empathetic AI companion'
}
```

#### Styling
Modify `tailwind.config.js` for custom themes and animations:

```javascript
theme: {
  extend: {
    colors: {
      // Add your custom colors
    },
    animation: {
      // Add custom animations
    }
  }
}
```

## 🏗️ Project Structure

```
ChatNova/
├── app/                    # Next.js app directory
│   ├── api/               # API routes
│   ├── globals.css        # Global styles
│   ├── layout.tsx         # Root layout
│   └── page.tsx           # Home page
├── components/            # React components
│   ├── ui/               # Base UI components
│   ├── chat/             # Chat-specific components
│   ├── avatar/           # Avatar components
│   ├── pdf/              # PDF handling components
│   └── layout/           # Layout components
├── lib/                  # Utility functions
│   ├── utils.ts          # General utilities
│   ├── gemini-api.ts     # AI integration
│   └── pdf-utils.ts      # PDF processing
├── stores/               # State management
│   └── chat-store.ts     # Chat state
├── hooks/                # Custom React hooks
└── public/               # Static assets
```

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

### Development Workflow

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Make your changes and test thoroughly
4. Commit your changes: `git commit -m 'Add amazing feature'`
5. Push to the branch: `git push origin feature/amazing-feature`
6. Open a Pull Request

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **Google Gemini AI** for powerful language model capabilities
- **Vercel** for excellent Next.js hosting and deployment
- **Radix UI** for accessible component primitives
- **Framer Motion** for smooth animations
- **Tailwind CSS** for utility-first styling

## 📞 Support

- **Documentation**: [docs.chatnova.ai](https://docs.chatnova.ai)
- **Issues**: [GitHub Issues](https://github.com/yourusername/chatnova/issues)
- **Discussions**: [GitHub Discussions](https://github.com/yourusername/chatnova/discussions)
- **Email**: <EMAIL>

---

<div align="center">
  <p>Made with ❤️ by the ChatNova Team</p>
  <p>
    <a href="https://chatnova.ai">Website</a> •
    <a href="https://docs.chatnova.ai">Documentation</a> •
    <a href="https://github.com/yourusername/chatnova">GitHub</a>
  </p>
</div>
