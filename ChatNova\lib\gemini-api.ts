import { GoogleGenerativeAI } from '@google/generative-ai'

// Initialize Gemini AI
const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY || '')

// Model configuration
const modelConfig = {
  temperature: 0.7,
  topK: 40,
  topP: 0.95,
  maxOutputTokens: 2048,
}

// Safety settings
const safetySettings = [
  {
    category: 'HARM_CATEGORY_HARASSMENT',
    threshold: 'BLOCK_MEDIUM_AND_ABOVE',
  },
  {
    category: 'HARM_CATEGORY_HATE_SPEECH',
    threshold: 'BLOCK_MEDIUM_AND_ABOVE',
  },
  {
    category: 'HARM_CATEGORY_SEXUALLY_EXPLICIT',
    threshold: 'BLOCK_MEDIUM_AND_ABOVE',
  },
  {
    category: 'HARM_CATEGORY_DANGEROUS_CONTENT',
    threshold: 'BLOCK_MEDIUM_AND_ABOVE',
  },
]

export async function sendMessage(message: string, context?: string): Promise<string> {
  try {
    if (!process.env.GEMINI_API_KEY) {
      throw new Error('Gemini API key is not configured')
    }

    const model = genAI.getGenerativeModel({ 
      model: 'gemini-pro',
      generationConfig: modelConfig,
      safetySettings: safetySettings as any,
    })

    // Construct the prompt with personality and context
    let prompt = `You are Nova, an intelligent, empathetic, and helpful AI companion. You have a warm, friendly personality and always strive to be understanding and supportive. You provide thoughtful, detailed responses while maintaining a conversational and engaging tone.

Key traits:
- Empathetic and emotionally intelligent
- Knowledgeable across many topics
- Helpful and solution-oriented
- Conversational and engaging
- Respectful and professional

`

    // Add document context if provided
    if (context && context.trim()) {
      prompt += `\nDocument Context:\n${context}\n\nPlease use this document context to inform your response when relevant. If the user is asking about the document, provide specific insights, summaries, or analysis based on the content.\n\n`
    }

    prompt += `User: ${message}\n\nNova:`

    const result = await model.generateContent(prompt)
    const response = await result.response
    const text = response.text()

    if (!text) {
      throw new Error('No response generated')
    }

    return text.trim()
  } catch (error) {
    console.error('Gemini API error:', error)
    
    if (error instanceof Error) {
      if (error.message.includes('API key')) {
        return "I'm sorry, but I'm not properly configured right now. Please check that the Gemini API key is set up correctly."
      } else if (error.message.includes('quota') || error.message.includes('limit')) {
        return "I'm experiencing high demand right now. Please try again in a moment."
      } else if (error.message.includes('safety')) {
        return "I can't provide a response to that request due to safety guidelines. Could you please rephrase your question?"
      }
    }
    
    return "I'm sorry, I encountered an error while processing your request. Please try again."
  }
}

export async function analyzeDocument(content: string, userQuery?: string): Promise<string> {
  try {
    if (!process.env.GEMINI_API_KEY) {
      throw new Error('Gemini API key is not configured')
    }

    const model = genAI.getGenerativeModel({ 
      model: 'gemini-pro',
      generationConfig: modelConfig,
      safetySettings: safetySettings as any,
    })

    let prompt = `You are Nova, an expert document analyst. Please analyze the following document content and provide insights.

Document Content:
${content}

`

    if (userQuery) {
      prompt += `Specific Question: ${userQuery}\n\nPlease answer the specific question based on the document content, and also provide additional relevant insights.`
    } else {
      prompt += `Please provide:
1. A comprehensive summary of the document
2. Key points and main themes
3. Important insights or findings
4. Any notable patterns or trends
5. Actionable recommendations if applicable

Format your response in a clear, organized manner with appropriate headings.`
    }

    const result = await model.generateContent(prompt)
    const response = await result.response
    const text = response.text()

    if (!text) {
      throw new Error('No analysis generated')
    }

    return text.trim()
  } catch (error) {
    console.error('Document analysis error:', error)
    return "I encountered an error while analyzing the document. Please try uploading the document again or contact support if the issue persists."
  }
}

export async function generateSummary(content: string, maxLength: number = 500): Promise<string> {
  try {
    if (!process.env.GEMINI_API_KEY) {
      throw new Error('Gemini API key is not configured')
    }

    const model = genAI.getGenerativeModel({ 
      model: 'gemini-pro',
      generationConfig: {
        ...modelConfig,
        maxOutputTokens: Math.min(maxLength * 2, 1024), // Rough token estimation
      },
      safetySettings: safetySettings as any,
    })

    const prompt = `Please provide a concise summary of the following content in approximately ${maxLength} characters or less. Focus on the most important points and key insights:

${content}

Summary:`

    const result = await model.generateContent(prompt)
    const response = await result.response
    const text = response.text()

    if (!text) {
      throw new Error('No summary generated')
    }

    return text.trim()
  } catch (error) {
    console.error('Summary generation error:', error)
    return "Unable to generate summary at this time."
  }
}

export async function extractKeyInsights(content: string): Promise<string[]> {
  try {
    if (!process.env.GEMINI_API_KEY) {
      throw new Error('Gemini API key is not configured')
    }

    const model = genAI.getGenerativeModel({
      model: 'gemini-pro',
      generationConfig: modelConfig,
      safetySettings: safetySettings as any,
    })

    const prompt = `Extract the top 5-10 key insights from the following content. Return each insight as a separate line starting with "- ":

${content}

Key Insights:`

    const result = await model.generateContent(prompt)
    const response = await result.response
    const text = response.text()

    if (!text) {
      return []
    }

    // Parse the response into an array of insights
    return text
      .split('\n')
      .filter(line => line.trim().startsWith('-'))
      .map(line => line.trim().substring(1).trim())
      .filter(insight => insight.length > 0)
  } catch (error) {
    console.error('Key insights extraction error:', error)
    return []
  }
}

export async function answerQuestion(question: string, context: string): Promise<string> {
  try {
    if (!process.env.GEMINI_API_KEY) {
      throw new Error('Gemini API key is not configured')
    }

    const model = genAI.getGenerativeModel({ 
      model: 'gemini-pro',
      generationConfig: modelConfig,
      safetySettings: safetySettings as any,
    })

    const prompt = `Based on the following context, please answer the user's question. If the answer cannot be found in the context, please say so clearly.

Context:
${context}

Question: ${question}

Answer:`

    const result = await model.generateContent(prompt)
    const response = await result.response
    const text = response.text()

    if (!text) {
      throw new Error('No answer generated')
    }

    return text.trim()
  } catch (error) {
    console.error('Question answering error:', error)
    return "I'm sorry, I couldn't process your question at this time. Please try again."
  }
}

// Utility function to check if API key is configured
export function isGeminiConfigured(): boolean {
  return !!process.env.GEMINI_API_KEY
}

// Utility function to estimate token count (rough approximation)
export function estimateTokenCount(text: string): number {
  // Rough estimation: 1 token ≈ 4 characters for English text
  return Math.ceil(text.length / 4)
}

// Utility function to truncate text to fit token limits
export function truncateToTokenLimit(text: string, maxTokens: number): string {
  const estimatedTokens = estimateTokenCount(text)
  if (estimatedTokens <= maxTokens) {
    return text
  }
  
  const maxChars = maxTokens * 4
  return text.substring(0, maxChars) + '...'
}
