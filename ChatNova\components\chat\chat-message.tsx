'use client'

import { motion } from 'framer-motion'
import { Message } from '@/stores/chat-store'
import { ChatAvatar } from '../avatar/chat-avatar'
import { Button } from '@/components/ui/button'
import { 
  Copy, 
  Volume2, 
  VolumeX, 
  FileText, 
  Clock,
  ThumbsUp,
  ThumbsDown,
  MoreHorizontal
} from 'lucide-react'
import { formatTime, copyToClipboard } from '@/lib/utils'
import { useState } from 'react'
import { useToast } from '@/hooks/use-toast'
import ReactMarkdown from 'react-markdown'

interface ChatMessageProps {
  message: Message
}

export function ChatMessage({ message }: ChatMessageProps) {
  const [isSpeaking, setIsSpeaking] = useState(false)
  const [showActions, setShowActions] = useState(false)
  const { toast } = useToast()

  const isUser = message.role === 'user'

  const handleCopy = async () => {
    const success = await copyToClipboard(message.content)
    toast({
      title: success ? "Copied!" : "Failed to copy",
      description: success ? "Message copied to clipboard" : "Please try again",
      variant: success ? "default" : "destructive",
    })
  }

  const handleSpeak = () => {
    if ('speechSynthesis' in window) {
      if (isSpeaking) {
        window.speechSynthesis.cancel()
        setIsSpeaking(false)
      } else {
        const utterance = new SpeechSynthesisUtterance(message.content)
        utterance.onstart = () => setIsSpeaking(true)
        utterance.onend = () => setIsSpeaking(false)
        utterance.onerror = () => setIsSpeaking(false)
        window.speechSynthesis.speak(utterance)
      }
    }
  }

  return (
    <motion.div
      className={`flex gap-3 ${isUser ? 'flex-row-reverse' : 'flex-row'} group`}
      onHoverStart={() => setShowActions(true)}
      onHoverEnd={() => setShowActions(false)}
      whileHover={{ scale: 1.01 }}
      transition={{ duration: 0.2 }}
    >
      {/* Avatar */}
      {!isUser && (
        <div className="flex-shrink-0">
          <ChatAvatar size="small" />
        </div>
      )}

      {/* Message Content */}
      <div className={`flex flex-col max-w-[80%] ${isUser ? 'items-end' : 'items-start'}`}>
        {/* Message Bubble */}
        <motion.div
          className={`relative px-4 py-3 rounded-2xl shadow-sm ${
            isUser 
              ? 'bg-primary text-primary-foreground ml-auto' 
              : 'bg-muted text-muted-foreground mr-auto glass-effect'
          }`}
          whileHover={{ 
            boxShadow: isUser 
              ? "0 8px 25px rgba(59, 130, 246, 0.3)" 
              : "0 8px 25px rgba(0, 0, 0, 0.1)" 
          }}
          transition={{ duration: 0.2 }}
        >
          {/* Message Type Indicator */}
          {message.type === 'pdf' && (
            <div className="flex items-center space-x-2 mb-2 text-xs opacity-70">
              <FileText className="w-3 h-3" />
              <span>PDF Analysis</span>
            </div>
          )}

          {/* Message Content */}
          <div className="prose prose-sm max-w-none dark:prose-invert">
            {isUser ? (
              <p className="m-0 whitespace-pre-wrap">{message.content}</p>
            ) : (
              <ReactMarkdown
                components={{
                  p: ({ children }) => <p className="m-0 last:mb-0">{children}</p>,
                  ul: ({ children }) => <ul className="my-2 ml-4">{children}</ul>,
                  ol: ({ children }) => <ol className="my-2 ml-4">{children}</ol>,
                  li: ({ children }) => <li className="my-1">{children}</li>,
                  code: ({ children }) => (
                    <code className="bg-black/10 dark:bg-white/10 px-1 py-0.5 rounded text-xs">
                      {children}
                    </code>
                  ),
                  pre: ({ children }) => (
                    <pre className="bg-black/10 dark:bg-white/10 p-3 rounded-lg overflow-x-auto my-2">
                      {children}
                    </pre>
                  ),
                }}
              >
                {message.content}
              </ReactMarkdown>
            )}
          </div>

          {/* Processing Time for PDF */}
          {message.metadata?.processingTime && (
            <div className="flex items-center space-x-1 mt-2 text-xs opacity-70">
              <Clock className="w-3 h-3" />
              <span>Processed in {message.metadata.processingTime}ms</span>
            </div>
          )}
        </motion.div>

        {/* Message Actions */}
        <AnimatePresence>
          {showActions && (
            <motion.div
              initial={{ opacity: 0, y: 5 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: 5 }}
              className={`flex items-center space-x-1 mt-2 ${
                isUser ? 'flex-row-reverse space-x-reverse' : 'flex-row'
              }`}
            >
              <Button
                variant="ghost"
                size="icon"
                onClick={handleCopy}
                className="h-6 w-6 hover:bg-white/10"
                title="Copy message"
              >
                <Copy className="w-3 h-3" />
              </Button>

              {!isUser && (
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={handleSpeak}
                  className="h-6 w-6 hover:bg-white/10"
                  title={isSpeaking ? "Stop speaking" : "Read aloud"}
                >
                  {isSpeaking ? (
                    <VolumeX className="w-3 h-3" />
                  ) : (
                    <Volume2 className="w-3 h-3" />
                  )}
                </Button>
              )}

              {!isUser && (
                <>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-6 w-6 hover:bg-green-500/20 hover:text-green-500"
                    title="Good response"
                  >
                    <ThumbsUp className="w-3 h-3" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-6 w-6 hover:bg-red-500/20 hover:text-red-500"
                    title="Poor response"
                  >
                    <ThumbsDown className="w-3 h-3" />
                  </Button>
                </>
              )}

              <Button
                variant="ghost"
                size="icon"
                className="h-6 w-6 hover:bg-white/10"
                title="More options"
              >
                <MoreHorizontal className="w-3 h-3" />
              </Button>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Timestamp */}
        <div className={`text-xs text-muted-foreground mt-1 ${
          isUser ? 'text-right' : 'text-left'
        }`}>
          {formatTime(message.timestamp)}
        </div>
      </div>

      {/* User Avatar */}
      {isUser && (
        <div className="flex-shrink-0">
          <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white text-sm font-semibold">
            U
          </div>
        </div>
      )}
    </motion.div>
  )
}
