import { GoogleGenerativeA<PERSON> } from "@google/generative-ai";
import { NextResponse } from "next/server";

export const maxDuration = 30;

// Initialize with the provided Gemini API key
const GEMINI_API_KEY = "AIzaSyBtSl3hn2Uj-64D5sDZ8ocVrD77ARE-uZo";
const genAI = new GoogleGenerativeAI(GEMINI_API_KEY);

// Define Message type
interface Message {
  role: string;
  content: string;
}

export async function POST(req: Request) {
  try {
    const { messages, uploadedFiles } = await req.json();

    // Enhanced system message for ChatNova with Gemini
    let systemPrompt = `You are <PERSON><PERSON><PERSON><PERSON>, an advanced AI assistant powered by Google's Gemini. You provide intelligent, detailed responses with a friendly and professional tone. You excel at reasoning, analysis, and creative problem-solving.

Key guidelines:
- Be conversational, helpful, and engaging
- Provide detailed, well-structured responses
- Use step-by-step thinking for complex queries
- Be honest when you don't know something
- Show your reasoning process when helpful
- Use clear, accessible language
- Be creative and innovative in your solutions`;

    // Add PDF content to the system message if files are uploaded
    if (uploadedFiles && uploadedFiles.length > 0) {
      systemPrompt += `\n\nYou have access to the following uploaded documents:\n`;
      uploadedFiles.forEach((file: any, index: number) => {
        systemPrompt += `\nDocument ${index + 1}: ${file.name}\nContent: ${file.content}\n`;
      });
      systemPrompt += `\nWhen answering questions, you can reference information from these documents. Always cite which document you're referencing when using information from the uploaded files.`;
    }

    // Get the Gemini model
    const model = genAI.getGenerativeModel({ model: "gemini-1.5-flash" });

    // Prepare the prompt with system message and user query
    const lastUserMessage = messages.filter((m: Message) => m.role === "user").pop()?.content || "Hello";
    const prompt = `${systemPrompt}\n\nUser query: ${lastUserMessage}`;

    // Generate response (non-streaming for simplicity)
    const result = await model.generateContent(prompt);
    const response = result.response;
    const text = response.text();

    return NextResponse.json({ 
      role: "assistant",
      content: text 
    });
  } catch (error) {
    console.error("Chat API error:", error);
    return NextResponse.json({ error: "Failed to generate response" }, { status: 500 });
  }
}
