"use client"

import type React from "react"
import { useRef, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card } from "@/components/ui/card"
import { MessageBubble } from "@/components/message-bubble"
import { FileUpload } from "@/components/file-upload"
import { VoiceInput } from "@/components/voice-input"
import { UploadedFiles } from "@/components/uploaded-files"
import { Send, Paperclip, Sparkles } from "lucide-react"
import { useState } from "react"
import type { Message } from "@ai-sdk/react"

interface ChatInterfaceProps {
  messages: Message[]
  input: string
  handleInputChange: (e: React.ChangeEvent<HTMLInputElement>) => void
  handleSubmit: (e: React.FormEvent<HTMLFormElement>) => void
  isLoading: boolean
  error: Error | undefined
  uploadedFiles: Array<{ name: string; content: string; type: string }>
  onFileUpload: (files: Array<{ name: string; content: string; type: string }>) => void
  onRemoveFile: (index: number) => void
}

export function ChatInterface({
  messages,
  input,
  handleInputChange,
  handleSubmit,
  isLoading,
  error,
  uploadedFiles,
  onFileUpload,
  onRemoveFile,
}: ChatInterfaceProps) {
  const [showFileUpload, setShowFileUpload] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" })
  }

  useEffect(() => {
    try {
      scrollToBottom()
    } catch (error) {
      // Silently handle any errors that might be related to extensions
      if (!(error instanceof Error) || (!error.message.includes("extension") && !error.message.includes("MetaMask"))) {
        console.error("Scroll error:", error)
      }
    }
  }, [messages])

  // Handle file upload and automatically hide the upload popup
  const handleFileUploadComplete = (files: Array<{ name: string; content: string; type: string }>) => {
    onFileUpload(files)
    setShowFileUpload(false) // Hide the upload popup after successful upload
  }

  return (
    <div className="flex flex-col h-[calc(100vh-200px)] max-w-5xl mx-auto">
      {/* Uploaded Files Display */}
      {uploadedFiles.length > 0 && (
        <div className="mb-6">
          <UploadedFiles files={uploadedFiles} onRemoveFile={onRemoveFile} />
        </div>
      )}

      {/* Messages Container */}
      <Card className="flex-1 overflow-hidden border-0 shadow-2xl bg-white/70 dark:bg-gray-800/70 backdrop-blur-xl">
        <div className="h-full overflow-y-auto p-6 space-y-6">
          {messages.length === 0 && (
            <div className="text-center text-gray-500 dark:text-gray-400 mt-12 space-y-4">
              <div className="w-16 h-16 mx-auto bg-gradient-to-br from-blue-500 to-purple-500 rounded-2xl flex items-center justify-center">
                <Sparkles className="w-8 h-8 text-white" />
              </div>
              <div>
                <p className="text-lg font-medium">Ready to chat with ChatNova!</p>
                <p className="text-sm">Ask me anything or upload a document to analyze.</p>
              </div>
            </div>
          )}

          {messages.map((message) => (
            <MessageBubble key={message.id} message={message} />
          ))}

          {isLoading && (
            <MessageBubble
              message={{
                id: "loading",
                role: "assistant",
                content: "",
                createdAt: new Date(),
              }}
              isLoading={true}
            />
          )}

          {error && (
            <div className="text-center text-red-500 p-6 bg-red-50 dark:bg-red-900/20 rounded-xl border border-red-200 dark:border-red-800">
              <p className="font-medium">Oops! Something went wrong.</p>
              <p className="text-sm mt-1">Please try again in a moment.</p>
            </div>
          )}

          <div ref={messagesEndRef} />
        </div>
      </Card>

      {/* File Upload Section */}
      {showFileUpload && (
        <div className="mt-6 animate-slide-down">
          <FileUpload onFileUpload={handleFileUploadComplete} />
        </div>
      )}

      {/* Enhanced Input Section */}
      <div className="mt-6 space-y-3">
        <form onSubmit={handleSubmit} className="flex gap-3">
          <div className="flex-1 relative">
            <Input
              value={input}
              onChange={handleInputChange}
              placeholder="Ask ChatNova anything..."
              disabled={isLoading}
              className="pr-24 h-14 text-base bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border-gray-200 dark:border-gray-700 focus:border-blue-400 dark:focus:border-blue-500 shadow-lg transition-all duration-200"
              autoFocus
            />
            <div className="absolute right-3 top-1/2 -translate-y-1/2 flex gap-1">
              <VoiceInput
                onTranscript={(transcript) => {
                  const event = {
                    target: { value: transcript },
                  } as React.ChangeEvent<HTMLInputElement>
                  handleInputChange(event)
                }}
              />
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => setShowFileUpload(!showFileUpload)}
                className="h-9 w-9 p-0 hover:bg-purple-100 dark:hover:bg-purple-900/30"
              >
                <Paperclip className="w-4 h-4" />
              </Button>
            </div>
          </div>
          <Button
            type="submit"
            disabled={isLoading || !input.trim()}
            className="h-14 px-8 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 disabled:opacity-50 disabled:hover:scale-100"
          >
            <Send className="w-5 h-5" />
          </Button>
        </form>

        <p className="text-xs text-center text-gray-500 dark:text-gray-400">
          Powered by Google Gemini • Advanced reasoning and intelligent analysis
        </p>
      </div>
    </div>
  )
}
