{"name": "chatnova", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@google/generative-ai": "^0.2.1", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-button": "^2.0.3", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.0.7", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "framer-motion": "^10.16.16", "lucide-react": "^0.294.0", "next": "14.0.4", "next-themes": "^0.2.1", "pdfjs-dist": "^4.0.379", "react": "^18", "react-dom": "^18", "react-dropzone": "^14.2.3", "react-markdown": "^9.0.1", "tailwind-merge": "^2.1.0", "tailwindcss-animate": "^1.0.7", "zustand": "^4.4.7"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "14.0.4", "postcss": "^8", "tailwindcss": "^3.3.0", "typescript": "^5"}}