# ChatNova Troubleshooting Guide

## Quick Start Issues

### 1. Node.js Not Found

**Error**: `'node' is not recognized as an internal or external command`

**Solution**:
1. Download and install Node.js from [nodejs.org](https://nodejs.org/)
2. Choose the LTS version (18.x or higher)
3. Restart your terminal/command prompt
4. Verify installation: `node -v` and `npm -v`

### 2. Permission Errors (macOS/Linux)

**Error**: `EACCES: permission denied`

**Solution**:
```bash
# Make the script executable
chmod +x run-project.sh

# Or use sudo for npm install (not recommended)
sudo npm install

# Better: Configure npm to use a different directory
npm config set prefix ~/.npm-global
export PATH=~/.npm-global/bin:$PATH
```

### 3. API Key Issues

**Error**: `Gemini API key is not configured`

**Solution**:
1. Get your API key from [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Copy `.env.local.example` to `.env.local`
3. Replace `your_gemini_api_key_here` with your actual key
4. Restart the development server

### 4. Port Already in Use

**Error**: `Port 3000 is already in use`

**Solution**:
```bash
# Kill process using port 3000
npx kill-port 3000

# Or use a different port
npm run dev -- -p 3001
```

## Common Runtime Errors

### 1. Module Not Found Errors

**Error**: `Module not found: Can't resolve '@/components/...'`

**Solution**:
```bash
# Clear node_modules and reinstall
rm -rf node_modules package-lock.json
npm install

# Or clear npm cache
npm cache clean --force
npm install
```

### 2. TypeScript Errors

**Error**: Various TypeScript compilation errors

**Solution**:
```bash
# Check TypeScript configuration
npx tsc --noEmit

# Update TypeScript
npm install typescript@latest

# Clear Next.js cache
rm -rf .next
npm run dev
```

### 3. PDF Upload Not Working

**Error**: PDF files not processing or uploading

**Possible Causes & Solutions**:

1. **File size too large**
   - Ensure PDF is under 10MB
   - Compress PDF if necessary

2. **Browser compatibility**
   - Use Chrome, Firefox, Safari, or Edge
   - Enable JavaScript

3. **CORS issues**
   - Check browser console for errors
   - Ensure proper CORS headers

### 4. Voice Features Not Working

**Error**: Microphone not working or voice input failing

**Solutions**:

1. **HTTPS Required**
   - Voice features require HTTPS in production
   - Use `https://localhost:3000` or deploy to HTTPS

2. **Browser Permissions**
   - Allow microphone access when prompted
   - Check browser settings for microphone permissions

3. **Browser Support**
   - Ensure browser supports Web Speech API
   - Chrome and Edge have best support

## Performance Issues

### 1. Slow Loading

**Solutions**:
```bash
# Enable production optimizations
npm run build
npm start

# Analyze bundle size
npm install --save-dev @next/bundle-analyzer
```

### 2. Memory Issues

**Solutions**:
```bash
# Increase Node.js memory limit
export NODE_OPTIONS="--max-old-space-size=4096"
npm run dev

# Or on Windows
set NODE_OPTIONS=--max-old-space-size=4096
npm run dev
```

## Development Issues

### 1. Hot Reload Not Working

**Solutions**:
```bash
# Clear Next.js cache
rm -rf .next

# Check file watchers (Linux)
echo fs.inotify.max_user_watches=524288 | sudo tee -a /etc/sysctl.conf
sudo sysctl -p
```

### 2. Environment Variables Not Loading

**Solutions**:
1. Ensure `.env.local` is in the root directory
2. Restart the development server after changes
3. Check variable names start with `NEXT_PUBLIC_` for client-side access
4. Verify no spaces around the `=` sign

### 3. Styling Issues

**Solutions**:
```bash
# Rebuild Tailwind CSS
npm run build

# Clear browser cache
# Use Ctrl+Shift+R (hard refresh)

# Check Tailwind configuration
npx tailwindcss -i ./app/globals.css -o ./output.css --watch
```

## API Issues

### 1. Gemini API Quota Exceeded

**Error**: `Quota exceeded` or `Rate limit`

**Solutions**:
1. Check your API usage in Google AI Studio
2. Wait for quota reset (usually daily)
3. Upgrade your API plan if needed
4. Implement request throttling

### 2. Network Errors

**Error**: `Failed to fetch` or connection timeouts

**Solutions**:
1. Check internet connection
2. Verify API key is valid
3. Check firewall/proxy settings
4. Try different network

## Browser-Specific Issues

### Chrome
- Clear cache and cookies
- Disable extensions temporarily
- Check developer console for errors

### Firefox
- Enable JavaScript
- Check privacy settings
- Clear site data

### Safari
- Enable developer tools
- Check privacy settings
- Allow cross-origin requests

## Deployment Issues

### Vercel
```bash
# Install Vercel CLI
npm i -g vercel

# Deploy
vercel

# Set environment variables
vercel env add GEMINI_API_KEY
```

### Netlify
```bash
# Build for static export
npm run build
npm run export

# Deploy the 'out' folder
```

## Getting Help

### Debug Information to Collect

1. **System Info**:
   ```bash
   node -v
   npm -v
   npx next info
   ```

2. **Browser Console Errors**:
   - Open Developer Tools (F12)
   - Check Console tab for errors
   - Check Network tab for failed requests

3. **Server Logs**:
   - Check terminal output for errors
   - Look for specific error messages

### Where to Get Help

1. **GitHub Issues**: Report bugs and feature requests
2. **Documentation**: Check README.md and setup.md
3. **Community**: Join discussions and ask questions
4. **Stack Overflow**: Search for similar issues

### Creating a Bug Report

Include:
- Operating system and version
- Node.js and npm versions
- Browser and version
- Steps to reproduce
- Error messages
- Screenshots if applicable

## Prevention Tips

1. **Keep Dependencies Updated**:
   ```bash
   npm audit
   npm update
   ```

2. **Use Stable Versions**:
   - Stick to LTS Node.js versions
   - Use stable package versions

3. **Regular Maintenance**:
   ```bash
   # Clean install periodically
   rm -rf node_modules package-lock.json
   npm install
   ```

4. **Monitor Performance**:
   - Use browser dev tools
   - Monitor API usage
   - Check bundle size regularly

## Emergency Reset

If all else fails, try a complete reset:

```bash
# 1. Backup your .env.local file
cp .env.local .env.local.backup

# 2. Clean everything
rm -rf node_modules package-lock.json .next

# 3. Reinstall
npm install

# 4. Restore environment
cp .env.local.backup .env.local

# 5. Start fresh
npm run dev
```

This should resolve most issues and get ChatNova running smoothly!
