"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { ScrollArea } from "@/components/ui/scroll-area"
import { ThemeToggle } from "@/components/theme-toggle"
import { <PERSON>rkles, Plus, MessageSquare, Trash2, ChevronLeft, ChevronRight, Clock } from "lucide-react"

interface ChatHistoryItem {
  id: string
  title: string
  timestamp: Date
  messages: any[]
}

interface ChatSidebarProps {
  chatHistory: ChatHistoryItem[]
  currentChatId: string | null
  onLoadChat: (chatId: string) => void
  onNewChat: () => void
  onDeleteChat: (chatId: string) => void
}

export function ChatSidebar({ chatHistory, currentChatId, onLoadChat, onNewChat, onDeleteChat }: ChatSidebarProps) {
  const [isCollapsed, setIsCollapsed] = useState(false)

  const formatTimestamp = (timestamp: Date) => {
    const now = new Date()
    const diff = now.getTime() - timestamp.getTime()
    const days = Math.floor(diff / (1000 * 60 * 60 * 24))

    if (days === 0) {
      return timestamp.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })
    } else if (days === 1) {
      return "Yesterday"
    } else if (days < 7) {
      return `${days} days ago`
    } else {
      return timestamp.toLocaleDateString()
    }
  }

  return (
    <div
      className={`relative bg-white/80 dark:bg-gray-900/80 backdrop-blur-xl border-r border-gray-200 dark:border-gray-700 transition-all duration-300 flex flex-col h-full ${
        isCollapsed ? "w-16" : "w-80"
      }`}
    >
      {/* Header */}
      <div className="p-4 border-b border-gray-200 dark:border-gray-700 flex-shrink-0">
        <div className="flex items-center justify-between">
          {!isCollapsed && (
            <div className="flex items-center gap-2">
              <div className="w-8 h-8 bg-gradient-to-br from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
                <Sparkles className="w-4 h-4 text-white" />
              </div>
              <div>
                <h2 className="font-semibold text-gray-800 dark:text-gray-200">ChatNova</h2>
                <p className="text-xs text-gray-500 dark:text-gray-400">AI Assistant</p>
              </div>
            </div>
          )}
          <div className="flex items-center gap-2">
            {!isCollapsed && <ThemeToggle />}
            <Button variant="ghost" size="sm" onClick={() => setIsCollapsed(!isCollapsed)} className="h-8 w-8 p-0">
              {isCollapsed ? <ChevronRight className="w-4 h-4" /> : <ChevronLeft className="w-4 h-4" />}
            </Button>
          </div>
        </div>
      </div>

      {!isCollapsed && (
        <>
          {/* New Chat Button */}
          <div className="p-4 flex-shrink-0">
            <Button
              onClick={onNewChat}
              className="w-full bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 shadow-lg"
            >
              <Plus className="w-4 h-4 mr-2" />
              New Chat
            </Button>
          </div>

          {/* Chat History - FIXED SCROLLING */}
          <div className="flex-1 min-h-0">
            <div className="px-4 pb-2">
              <h3 className="text-sm font-medium text-gray-600 dark:text-gray-400 flex items-center gap-2">
                <Clock className="w-4 h-4" />
                Recent Chats
              </h3>
            </div>
            <ScrollArea className="h-full">
              <div className="px-4 space-y-2 pb-4">
                {chatHistory.length === 0 ? (
                  <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                    <MessageSquare className="w-8 h-8 mx-auto mb-2 opacity-50" />
                    <p className="text-sm">No chat history yet</p>
                  </div>
                ) : (
                  chatHistory.map((chat) => (
                    <Card
                      key={chat.id}
                      className={`cursor-pointer transition-all duration-200 hover:shadow-md group ${
                        currentChatId === chat.id
                          ? "bg-purple-50 dark:bg-purple-900/20 border-purple-200 dark:border-purple-700"
                          : "hover:bg-gray-50 dark:hover:bg-gray-800/50"
                      }`}
                      onClick={() => onLoadChat(chat.id)}
                    >
                      <CardContent className="p-3">
                        <div className="flex items-start justify-between">
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium text-gray-800 dark:text-gray-200 truncate">
                              {chat.title}
                            </p>
                            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                              {formatTimestamp(chat.timestamp)}
                            </p>
                            <p className="text-xs text-gray-400 dark:text-gray-500 mt-1">
                              {chat.messages.length} messages
                            </p>
                          </div>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation()
                              onDeleteChat(chat.id)
                            }}
                            className="opacity-0 group-hover:opacity-100 transition-opacity h-6 w-6 p-0 text-red-500 hover:text-red-700"
                          >
                            <Trash2 className="w-3 h-3" />
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  ))
                )}
              </div>
            </ScrollArea>
          </div>
        </>
      )}

      {isCollapsed && (
        <div className="p-2 space-y-2 flex-shrink-0">
          <Button variant="ghost" size="sm" onClick={onNewChat} className="w-full h-10 p-0" title="New Chat">
            <Plus className="w-4 h-4" />
          </Button>
          <ThemeToggle />
        </div>
      )}
    </div>
  )
}
