'use client'

import { useState, useCallback } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useDropzone } from 'react-dropzone'
import { Button } from '@/components/ui/button'
import { 
  Upload, 
  FileText, 
  X, 
  CheckCircle, 
  AlertCircle,
  Loader2,
  Download
} from 'lucide-react'
import { useChatStore } from '@/stores/chat-store'
import { extractPDFText } from '@/lib/pdf-utils'
import { useToast } from '@/hooks/use-toast'
import { formatFileSize } from '@/lib/utils'

interface PDFUploadProps {
  onClose: () => void
}

export function PDFUpload({ onClose }: PDFUploadProps) {
  const [isProcessing, setIsProcessing] = useState(false)
  const [processedFiles, setProcessedFiles] = useState<string[]>([])
  const [errors, setErrors] = useState<string[]>([])
  
  const { addUploadedFile, addExtractedContent } = useChatStore()
  const { toast } = useToast()

  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    setIsProcessing(true)
    setErrors([])
    
    for (const file of acceptedFiles) {
      try {
        // Validate file
        if (file.type !== 'application/pdf') {
          throw new Error(`${file.name} is not a PDF file`)
        }
        
        if (file.size > 10 * 1024 * 1024) { // 10MB limit
          throw new Error(`${file.name} is too large (max 10MB)`)
        }

        // Extract text from PDF
        const extractedText = await extractPDFText(file)
        
        if (!extractedText.trim()) {
          throw new Error(`${file.name} appears to be empty or contains no readable text`)
        }

        // Add to store
        addUploadedFile(file)
        addExtractedContent(extractedText)
        setProcessedFiles(prev => [...prev, file.name])

        toast({
          title: "PDF processed successfully",
          description: `Extracted ${extractedText.length} characters from ${file.name}`,
        })

      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : `Failed to process ${file.name}`
        setErrors(prev => [...prev, errorMessage])
        
        toast({
          title: "Processing failed",
          description: errorMessage,
          variant: "destructive",
        })
      }
    }
    
    setIsProcessing(false)
  }, [addUploadedFile, addExtractedContent, toast])

  const { getRootProps, getInputProps, isDragActive, isDragReject } = useDropzone({
    onDrop,
    accept: {
      'application/pdf': ['.pdf']
    },
    maxFiles: 5,
    disabled: isProcessing
  })

  const handleClose = () => {
    if (processedFiles.length > 0) {
      toast({
        title: "Files ready",
        description: `${processedFiles.length} PDF(s) processed and ready for analysis`,
      })
    }
    onClose()
  }

  return (
    <div className="p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-xl font-semibold">Upload PDF Documents</h2>
          <p className="text-sm text-muted-foreground mt-1">
            Upload PDF files to extract and analyze their content
          </p>
        </div>
        <Button
          variant="ghost"
          size="icon"
          onClick={handleClose}
          className="hover:bg-white/10"
        >
          <X className="w-4 h-4" />
        </Button>
      </div>

      {/* Upload Area */}
      <motion.div
        {...getRootProps()}
        className={`
          pdf-upload-zone cursor-pointer transition-all duration-300
          ${isDragActive ? 'dragover' : ''}
          ${isDragReject ? 'border-red-500 bg-red-500/5' : ''}
          ${isProcessing ? 'pointer-events-none opacity-50' : ''}
        `}
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
      >
        <input {...getInputProps()} />
        
        <div className="flex flex-col items-center space-y-4">
          <motion.div
            animate={isDragActive ? { scale: 1.1 } : { scale: 1 }}
            className="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center"
          >
            {isProcessing ? (
              <Loader2 className="w-8 h-8 text-white animate-spin" />
            ) : (
              <Upload className="w-8 h-8 text-white" />
            )}
          </motion.div>
          
          <div className="text-center">
            <h3 className="text-lg font-semibold mb-2">
              {isDragActive ? 'Drop your PDFs here' : 'Upload PDF Documents'}
            </h3>
            <p className="text-muted-foreground">
              {isProcessing 
                ? 'Processing your documents...'
                : 'Drag & drop PDF files here, or click to browse'
              }
            </p>
            <p className="text-xs text-muted-foreground mt-2">
              Max 5 files, 10MB each • PDF format only
            </p>
          </div>
        </div>
      </motion.div>

      {/* Processing Status */}
      <AnimatePresence>
        {(processedFiles.length > 0 || errors.length > 0) && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="mt-6 space-y-3"
          >
            <h4 className="font-medium">Processing Results</h4>
            
            {/* Successful Files */}
            {processedFiles.map((fileName, index) => (
              <motion.div
                key={fileName}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
                className="flex items-center space-x-3 p-3 bg-green-500/10 border border-green-500/20 rounded-lg"
              >
                <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium truncate">{fileName}</p>
                  <p className="text-xs text-muted-foreground">
                    Successfully processed and ready for analysis
                  </p>
                </div>
              </motion.div>
            ))}

            {/* Error Files */}
            {errors.map((error, index) => (
              <motion.div
                key={error}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: (processedFiles.length + index) * 0.1 }}
                className="flex items-center space-x-3 p-3 bg-red-500/10 border border-red-500/20 rounded-lg"
              >
                <AlertCircle className="w-5 h-5 text-red-500 flex-shrink-0" />
                <div className="flex-1 min-w-0">
                  <p className="text-sm text-red-600 dark:text-red-400">{error}</p>
                </div>
              </motion.div>
            ))}
          </motion.div>
        )}
      </AnimatePresence>

      {/* Action Buttons */}
      <div className="flex justify-end space-x-3 mt-6 pt-6 border-t border-border/50">
        <Button
          variant="outline"
          onClick={onClose}
          disabled={isProcessing}
        >
          Cancel
        </Button>
        
        <Button
          variant="gradient"
          onClick={handleClose}
          disabled={isProcessing || processedFiles.length === 0}
        >
          {processedFiles.length > 0 ? (
            <>
              <FileText className="w-4 h-4 mr-2" />
              Use {processedFiles.length} File{processedFiles.length !== 1 ? 's' : ''}
            </>
          ) : (
            'Upload Files'
          )}
        </Button>
      </div>

      {/* Tips */}
      <div className="mt-6 p-4 bg-muted/30 rounded-lg">
        <h5 className="text-sm font-medium mb-2">💡 Tips for better results:</h5>
        <ul className="text-xs text-muted-foreground space-y-1">
          <li>• Use high-quality PDFs with clear, readable text</li>
          <li>• Scanned documents work best when text is crisp and well-lit</li>
          <li>• Multiple files will be analyzed together for comprehensive insights</li>
          <li>• Large documents may take a few moments to process</li>
        </ul>
      </div>
    </div>
  )
}
