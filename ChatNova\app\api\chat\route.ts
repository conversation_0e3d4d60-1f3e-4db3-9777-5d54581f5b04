import { NextRequest, NextResponse } from 'next/server'
import { sendMessage, analyzeDocument } from '@/lib/gemini-api'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { message, context, type = 'text' } = body

    if (!message && !context) {
      return NextResponse.json(
        { error: 'Message or context is required' },
        { status: 400 }
      )
    }

    let response: string

    if (type === 'pdf' && context) {
      // Handle PDF analysis
      response = await analyzeDocument(context, message)
    } else {
      // Handle regular chat
      response = await sendMessage(message, context)
    }

    return NextResponse.json({ response })
  } catch (error) {
    console.error('Chat API error:', error)
    
    return NextResponse.json(
      { error: 'Failed to process request' },
      { status: 500 }
    )
  }
}

export async function GET() {
  return NextResponse.json(
    { message: 'ChatNova API is running' },
    { status: 200 }
  )
}
