'use client'

import { motion } from 'framer-motion'
import { ChatAvatar } from '../avatar/chat-avatar'
import { useChatStore } from '@/stores/chat-store'

export function TypingIndicator() {
  const { personality } = useChatStore()

  return (
    <div className="flex gap-3">
      {/* Avatar */}
      <div className="flex-shrink-0">
        <ChatAvatar size="small" isTyping />
      </div>

      {/* Typing Bubble */}
      <motion.div
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
        className="bg-muted text-muted-foreground px-4 py-3 rounded-2xl glass-effect max-w-xs"
      >
        <div className="flex items-center space-x-2">
          <span className="text-sm">{personality.name} is typing</span>
          <div className="flex space-x-1">
            {[0, 1, 2].map((i) => (
              <motion.div
                key={i}
                className="w-2 h-2 bg-current rounded-full opacity-60"
                animate={{
                  scale: [1, 1.2, 1],
                  opacity: [0.6, 1, 0.6],
                }}
                transition={{
                  duration: 1.5,
                  repeat: Infinity,
                  delay: i * 0.2,
                  ease: "easeInOut",
                }}
              />
            ))}
          </div>
        </div>
      </motion.div>
    </div>
  )
}
