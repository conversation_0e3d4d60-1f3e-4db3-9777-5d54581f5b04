"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { FileUpload } from "@/components/file-upload"
import { MessageCircle, FileText, Mic, Sparkles, Zap, Brain, Lightbulb } from "lucide-react"
import { ThemeToggle } from "@/components/theme-toggle"

interface WelcomeScreenProps {
  onFileUpload: (files: Array<{ name: string; content: string; type: string }>) => void
  onStartChat: () => void
}

export function WelcomeScreen({ onFileUpload, onStartChat }: WelcomeScreenProps) {
  const [showUpload, setShowUpload] = useState(false)

  const features = [
    {
      icon: Brain,
      title: "Advanced Intelligence",
      description: "Google Gemini's cutting-edge AI for complex reasoning and analysis",
      color: "from-blue-500 to-blue-600",
    },
    {
      icon: FileText,
      title: "Document Analysis",
      description: "Upload PDFs and get intelligent insights and comprehensive summaries",
      color: "from-purple-500 to-purple-600",
    },
    {
      icon: Mic,
      title: "Voice Interaction",
      description: "Natural voice conversations with advanced speech recognition",
      color: "from-green-500 to-green-600",
    },
    {
      icon: Lightbulb,
      title: "Creative Solutions",
      description: "Innovative problem-solving and creative thinking capabilities",
      color: "from-orange-500 to-orange-600",
    },
  ]

  const quickStarters = [
    "Explain complex topics in simple terms",
    "Analyze this document for key insights",
    "Help me solve a challenging problem",
    "Create a comprehensive summary",
  ]

  return (
    <div className="space-y-8 animate-fade-in max-w-6xl mx-auto pb-12">
      {/* Header with logo and theme toggle */}
      <header className="flex items-center justify-between mb-8">
        <div className="text-center flex-1">
          <div className="flex items-center justify-center gap-3 mb-2">
            <div className="relative">
              <div className="w-12 h-12 bg-gradient-to-br from-blue-600 to-purple-600 rounded-xl flex items-center justify-center shadow-lg transform hover:rotate-12 transition-transform duration-300">
                <Sparkles className="w-6 h-6 text-white" />
              </div>
              <div className="absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white dark:border-gray-800 animate-pulse"></div>
            </div>
            <div>
              <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                ChatNova
              </h1>
              <p className="text-gray-600 dark:text-gray-300 text-sm">
                Powered by Google Gemini • Advanced AI Assistant
              </p>
            </div>
          </div>
        </div>
        <ThemeToggle />
      </header>

      <div className="text-center space-y-6">
        <div className="relative mx-auto w-24 h-24">
          <div className="absolute inset-0 bg-gradient-to-br from-purple-600 to-blue-600 rounded-2xl animate-pulse"></div>
          <div className="relative w-full h-full bg-gradient-to-br from-purple-500 to-blue-500 rounded-2xl flex items-center justify-center shadow-2xl">
            <Sparkles className="w-12 h-12 text-white animate-bounce" />
          </div>
          <div className="absolute -top-2 -right-2 w-6 h-6 bg-green-500 rounded-full border-2 border-white dark:border-gray-800 animate-ping"></div>
        </div>

        <div className="space-y-3">
          <h2 className="text-3xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-blue-600 bg-clip-text text-transparent">
            Welcome to ChatNova
          </h2>
          <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto text-lg">
            Experience advanced AI powered by Google Gemini. Get intelligent responses, document analysis, and creative
            solutions for any challenge!
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {features.map((feature, index) => (
          <Card
            key={index}
            className="group border-0 shadow-lg hover:shadow-2xl transition-all duration-300 hover:-translate-y-1 bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm"
          >
            <CardContent className="p-6 text-center">
              <div
                className={`w-12 h-12 mx-auto mb-4 bg-gradient-to-r ${feature.color} rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300`}
              >
                <feature.icon className="w-6 h-6 text-white" />
              </div>
              <h3 className="font-semibold text-gray-800 dark:text-gray-200 mb-2 text-lg">{feature.title}</h3>
              <p className="text-sm text-gray-600 dark:text-gray-400 leading-relaxed">{feature.description}</p>
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="space-y-4">
        <h3 className="text-xl font-semibold text-center text-gray-800 dark:text-gray-200">Quick Starters</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          {quickStarters.map((starter, index) => (
            <Button
              key={index}
              variant="outline"
              onClick={() => {
                onStartChat()
                // You could auto-fill the input with this starter
              }}
              className="h-auto p-4 text-left justify-start hover:bg-purple-50 dark:hover:bg-purple-900/20 border-purple-200 dark:border-purple-700 transition-all duration-200 hover:border-purple-400"
            >
              <Zap className="w-4 h-4 mr-3 text-purple-600 flex-shrink-0" />
              <span className="text-sm">{starter}</span>
            </Button>
          ))}
        </div>
      </div>

      <div className="flex flex-col sm:flex-row gap-4 justify-center">
        <Button
          onClick={onStartChat}
          size="lg"
          className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
        >
          <MessageCircle className="w-5 h-5 mr-2" />
          Start Conversation
        </Button>
        <Button
          onClick={() => setShowUpload(!showUpload)}
          variant="outline"
          size="lg"
          className="border-purple-200 dark:border-purple-700 hover:bg-purple-50 dark:hover:bg-purple-900/20 transition-all duration-200"
        >
          <FileText className="w-5 h-5 mr-2" />
          Upload Document
        </Button>
      </div>

      {showUpload && (
        <div className="animate-slide-down">
          <FileUpload onFileUpload={onFileUpload} />
        </div>
      )}
    </div>
  )
}
