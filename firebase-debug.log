[debug] [2025-05-29T14:33:04.271Z] ----------------------------------------------------------------------
[debug] [2025-05-29T14:33:04.273Z] Command:       C:\Program Files\nodejs\node.exe C:\Users\<USER>\AppData\Roaming\npm\node_modules\firebase-tools\lib\bin\firebase.js init
[debug] [2025-05-29T14:33:04.273Z] CLI Version:   14.3.1
[debug] [2025-05-29T14:33:04.273Z] Platform:      win32
[debug] [2025-05-29T14:33:04.273Z] Node Version:  v22.14.0
[debug] [2025-05-29T14:33:04.273Z] Time:          Thu May 29 2025 20:03:04 GMT+0530 (India Standard Time)
[debug] [2025-05-29T14:33:04.273Z] ----------------------------------------------------------------------
[debug] 
[debug] [2025-05-29T14:33:04.277Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-05-29T14:33:04.278Z] > authorizing via signed-in user (<EMAIL>)
[info] 
     ######## #### ########  ######## ########     ###     ######  ########
     ##        ##  ##     ## ##       ##     ##  ##   ##  ##       ##
     ######    ##  ########  ######   ########  #########  ######  ######
     ##        ##  ##    ##  ##       ##     ## ##     ##       ## ##
     ##       #### ##     ## ######## ########  ##     ##  ######  ########

You're about to initialize a Firebase project in this directory:

  C:\Users\<USER>\OneDrive\Desktop\Coding\ChatNova

[debug] [2025-05-29T14:33:13.864Z] ExitPromptError: User force closed the prompt with SIGINT
    at Interface.sigint (C:\Users\<USER>\AppData\Roaming\npm\node_modules\firebase-tools\node_modules\@inquirer\core\dist\commonjs\lib\create-prompt.js:100:37)
    at Interface.emit (node:events:518:28)
    at Interface.emit (node:domain:489:12)
    at [_ttyWrite] [as _ttyWrite] (node:internal/readline/interface:1124:18)
    at ReadStream.onkeypress (node:internal/readline/interface:263:20)
    at ReadStream.emit (node:events:530:35)
    at ReadStream.emit (node:domain:489:12)
    at emitKeys (node:internal/readline/utils:370:14)
    at emitKeys.next (<anonymous>)
    at ReadStream.onData (node:internal/readline/emitKeypressEvents:64:36)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-05-29T14:33:20.324Z] ----------------------------------------------------------------------
[debug] [2025-05-29T14:33:20.326Z] Command:       C:\Program Files\nodejs\node.exe C:\Users\<USER>\AppData\Roaming\npm\node_modules\firebase-tools\lib\bin\firebase.js init hosting
[debug] [2025-05-29T14:33:20.326Z] CLI Version:   14.3.1
[debug] [2025-05-29T14:33:20.326Z] Platform:      win32
[debug] [2025-05-29T14:33:20.327Z] Node Version:  v22.14.0
[debug] [2025-05-29T14:33:20.327Z] Time:          Thu May 29 2025 20:03:20 GMT+0530 (India Standard Time)
[debug] [2025-05-29T14:33:20.327Z] ----------------------------------------------------------------------
[debug] 
[debug] [2025-05-29T14:33:20.330Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-05-29T14:33:20.331Z] > authorizing via signed-in user (<EMAIL>)
[info] 
     ######## #### ########  ######## ########     ###     ######  ########
     ##        ##  ##     ## ##       ##     ##  ##   ##  ##       ##
     ######    ##  ########  ######   ########  #########  ######  ######
     ##        ##  ##    ##  ##       ##     ## ##     ##       ## ##
     ##       #### ##     ## ######## ########  ##     ##  ######  ########

You're about to initialize a Firebase project in this directory:

  c:\Users\<USER>\OneDrive\Desktop\Coding\ChatNova

[debug] [2025-05-29T14:34:05.811Z] ----------------------------------------------------------------------
[debug] [2025-05-29T14:34:05.813Z] Command:       C:\Program Files\nodejs\node.exe C:\Users\<USER>\AppData\Roaming\npm\node_modules\firebase-tools\lib\bin\firebase.js init
[debug] [2025-05-29T14:34:05.814Z] CLI Version:   14.3.1
[debug] [2025-05-29T14:34:05.814Z] Platform:      win32
[debug] [2025-05-29T14:34:05.814Z] Node Version:  v22.14.0
[debug] [2025-05-29T14:34:05.814Z] Time:          Thu May 29 2025 20:04:05 GMT+0530 (India Standard Time)
[debug] [2025-05-29T14:34:05.814Z] ----------------------------------------------------------------------
[debug] 
[debug] [2025-05-29T14:34:05.818Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-05-29T14:34:05.818Z] > authorizing via signed-in user (<EMAIL>)
[info] 
     ######## #### ########  ######## ########     ###     ######  ########
     ##        ##  ##     ## ##       ##     ##  ##   ##  ##       ##
     ######    ##  ########  ######   ########  #########  ######  ######
     ##        ##  ##    ##  ##       ##     ## ##     ##       ## ##
     ##       #### ##     ## ######## ########  ##     ##  ######  ########

You're about to initialize a Firebase project in this directory:

  C:\Users\<USER>\OneDrive\Desktop\Coding\ChatNova

[info] 
=== Project Setup
[info] 
[info] First, let's associate this project directory with a Firebase project.
[info] You can create multiple project aliases by running firebase use --add, 
[info] but for now we'll just set up a default project.
[info] 
[info] i  If you want to create a project in a Google Cloud organization or folder, please use "firebase projects:create" instead, and return to this command when you've created the project. 
[debug] [2025-05-29T14:34:52.707Z] Checked if tokens are valid: false, expires at: 1748529361726
[debug] [2025-05-29T14:34:52.708Z] Checked if tokens are valid: false, expires at: 1748529361726
[debug] [2025-05-29T14:34:52.708Z] > refreshing access token with scopes: []
[debug] [2025-05-29T14:34:52.709Z] >>> [apiv2][query] POST https://www.googleapis.com/oauth2/v3/token [none]
[debug] [2025-05-29T14:34:52.709Z] >>> [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-05-29T14:34:53.205Z] <<< [apiv2][status] POST https://www.googleapis.com/oauth2/v3/token 200
[debug] [2025-05-29T14:34:53.205Z] <<< [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-05-29T14:34:53.211Z] >>> [apiv2][query] POST https://cloudresourcemanager.googleapis.com/v1/projects [none]
[debug] [2025-05-29T14:34:53.211Z] >>> [apiv2][body] POST https://cloudresourcemanager.googleapis.com/v1/projects {"projectId":"Chatbot","name":"ChatNova"}
[debug] [2025-05-29T14:34:55.019Z] <<< [apiv2][status] POST https://cloudresourcemanager.googleapis.com/v1/projects 400
[debug] [2025-05-29T14:34:55.019Z] <<< [apiv2][body] POST https://cloudresourcemanager.googleapis.com/v1/projects {"error":{"code":400,"message":"field [project_id] has issue [project_id contains invalid characters]","status":"INVALID_ARGUMENT","details":[{"@type":"type.googleapis.com/google.rpc.BadRequest","fieldViolations":[{"field":"project_id","description":"project_id contains invalid characters"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"url":"https://cloud.google.com/resource-manager/reference/rest/v1/projects"}]}]}}
[debug] [2025-05-29T14:34:55.455Z] FirebaseError: Request to https://cloudresourcemanager.googleapis.com/v1/projects had HTTP Error: 400, field [project_id] has issue [project_id contains invalid characters]
    at responseToError (C:\Users\<USER>\AppData\Roaming\npm\node_modules\firebase-tools\lib\responseToError.js:52:12)
    at RetryOperation._fn (C:\Users\<USER>\AppData\Roaming\npm\node_modules\firebase-tools\lib\apiv2.js:312:77)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
[error] 
[error] Error: Failed to create project. See firebase-debug.log for more info.
