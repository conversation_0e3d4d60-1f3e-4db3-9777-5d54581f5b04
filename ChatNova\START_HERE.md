# 🚀 ChatNova - Quick Start Guide

Welcome to <PERSON>t<PERSON>ova! This guide will get you up and running in minutes.

## 📋 Prerequisites

Before starting, ensure you have:

1. **Node.js 18+** - [Download here](https://nodejs.org/)
2. **Google Gemini API Key** - [Get one here](https://makersuite.google.com/app/apikey)

## ⚡ Quick Start (3 Steps)

### Step 1: Install Node.js
- Download from [nodejs.org](https://nodejs.org/)
- Choose the LTS version (recommended)
- Verify installation: Open terminal and run `node -v`

### Step 2: Get Your API Key
1. Go to [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Sign in with your Google account
3. Click "Create API Key"
4. Copy the generated key

### Step 3: Run ChatNova

**Option A: Windows (Double-click)**
1. Double-click `run-project.bat`
2. Follow the prompts
3. Enter your API key when asked

**Option B: Manual Setup**
```bash
# Install dependencies
npm install

# Copy environment file
cp .env.local.example .env.local

# Edit .env.local and add your API key
# GEMINI_API_KEY=your_actual_api_key_here

# Start the development server
npm run dev
```

**Option C: macOS/Linux**
```bash
# Make script executable
chmod +x run-project.sh

# Run the script
./run-project.sh
```

## 🌐 Access Your App

Once running, open your browser and go to:
**http://localhost:3000**

## ✨ What You'll See

1. **Welcome Screen**: Beautiful introduction with feature overview
2. **Chat Interface**: Start conversations with Nova, your AI companion
3. **PDF Upload**: Drag & drop PDFs for intelligent analysis
4. **Voice Features**: Enable voice input/output in the header
5. **Dark/Light Mode**: Toggle themes with the sun/moon icon

## 🎯 Key Features to Try

### 💬 Chat with AI
- Type any message and press Enter
- Ask questions about any topic
- Experience empathetic, intelligent responses

### 📄 PDF Analysis
- Click the paperclip icon or drag PDFs to upload
- Ask questions about your documents
- Get summaries, insights, and key points

### 🎤 Voice Interaction
- Click the microphone icon in the header to enable
- Use the mic button in chat for voice input
- Hear responses read aloud

### 🎨 Customization
- Toggle dark/light mode
- Create multiple chat sessions
- Manage conversation history

## 🔧 Troubleshooting

### Common Issues

**"Node.js not found"**
- Install Node.js from nodejs.org
- Restart your terminal

**"API key not configured"**
- Edit `.env.local` file
- Add your Gemini API key
- Restart the server

**"Port 3000 in use"**
- Run: `npx kill-port 3000`
- Or use: `npm run dev -- -p 3001`

**PDF upload not working**
- Ensure file is under 10MB
- Use modern browser (Chrome, Firefox, Safari, Edge)
- Check browser console for errors

**Voice features not working**
- Allow microphone permissions
- Use HTTPS in production
- Try Chrome or Edge for best support

### Need More Help?

1. Check `TROUBLESHOOTING.md` for detailed solutions
2. Review `README.md` for complete documentation
3. Check browser console for error messages
4. Ensure your API key is valid and has quota

## 🎉 You're Ready!

ChatNova is now running! Here's what to explore:

1. **Start with a simple question**: "Hello, how are you?"
2. **Upload a PDF**: Try the document analysis feature
3. **Enable voice**: Experience hands-free interaction
4. **Switch themes**: Try both dark and light modes
5. **Create sessions**: Organize different conversations

## 📚 Next Steps

- **Customize**: Edit personality settings in `stores/chat-store.ts`
- **Deploy**: Use Vercel, Netlify, or other platforms (see `DEPLOYMENT.md`)
- **Extend**: Add new features in the `components/` directory
- **Style**: Modify themes in `tailwind.config.js`

## 🌟 Pro Tips

- **Keyboard Shortcuts**: 
  - Enter = Send message
  - Shift+Enter = New line
  - Ctrl+/ = Focus input

- **PDF Tips**:
  - Multiple files are analyzed together
  - Clear text works better than scanned images
  - Ask specific questions for better results

- **Voice Tips**:
  - Speak clearly and at normal pace
  - Use in quiet environment for best results
  - HTTPS required for production use

## 🎊 Enjoy ChatNova!

You now have a fully functional, next-generation AI chatbot with:
- ✅ Intelligent conversations powered by Gemini AI
- ✅ PDF document analysis and insights
- ✅ Voice input and output capabilities
- ✅ Beautiful, responsive design
- ✅ Dark/light theme support
- ✅ Session management and history

Happy chatting! 🚀✨

---

**Need help?** Check the other documentation files:
- `README.md` - Complete overview
- `TROUBLESHOOTING.md` - Detailed problem solving
- `DEPLOYMENT.md` - Production deployment
- `setup.md` - Detailed setup instructions
