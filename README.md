<div align="center">

# 🌟 ChatNova 🤖

<img src="https://raw.githubusercontent.com/Sagexd08/ChatNova/main/public/placeholder.svg" alt="ChatNova Preview" width="700px" style="border-radius: 10px; box-shadow: 0 5px 15px rgba(0,0,0,0.2);" />

### *Next-Generation AI Chatbot Interface*

[![Deployed on Vercel](https://img.shields.io/badge/Deployed%20on-Vercel-000000?style=for-the-badge&logo=vercel&logoColor=white)](https://vercel.com/sohomchatterjee07-gmailcoms-projects/v0-chatbot-ui-ux-design)
[![Built with v0](https://img.shields.io/badge/Built%20with-v0.dev-000000?style=for-the-badge&logo=v&logoColor=white)](https://v0.dev/chat/projects/F6M4g0l0gt0)
[![Next.js](https://img.shields.io/badge/Next.js-000000?style=for-the-badge&logo=next.js&logoColor=white)](https://nextjs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-3178C6?style=for-the-badge&logo=typescript&logoColor=white)](https://www.typescriptlang.org/)
[![Tailwind CSS](https://img.shields.io/badge/Tailwind_CSS-06B6D4?style=for-the-badge&logo=tailwind-css&logoColor=white)](https://tailwindcss.com/)

*Automatically synced with your [v0.dev](https://v0.dev) deployments*

</div>

<hr style="background: linear-gradient(to right, #6a11cb, #2575fc); height: 3px; border: none;">

<div align="center">
  <h2>🚀 Experience the Future of Conversational AI</h2>
</div>

> **ChatNova** combines cutting-edge design with powerful functionality to create an exceptional chatbot experience. With features like voice input, PDF analysis, and a stunning parallax background, this isn't just another chat interface—it's a revolution in human-AI interaction.

<br>

## 📋 Table of Contents

<div align="center">

| 🔍 [Overview](#-overview) | ✨ [Features](#-features) | 🌐 [Live Demo](#-live-demo) | 🛠️ [Build](#-build-your-app) |
|:------------------------:|:------------------------:|:---------------------------:|:----------------------------:|
| 🔄 [How It Works](#-how-it-works) | 💻 [Technologies](#-technologies) | 📁 [Structure](#-project-structure) | 🚦 [Getting Started](#-getting-started) |

</div>

<hr style="background: linear-gradient(to right, #11998e, #38ef7d); height: 2px; border: none;">

## 🔍 Overview

<div style="background-color: #f8f9fa; border-left: 4px solid #6a11cb; padding: 15px; border-radius: 5px;">
ChatNova is a modern, responsive chatbot interface designed to provide an exceptional user experience. This repository stays in sync with your deployed chats on <a href="https://v0.dev">v0.dev</a>, ensuring that any changes you make to your deployed app are automatically pushed to this repository.
</div>

<br>

## ✨ Features

<div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 10px;">
  <div style="background-color: #f0f7ff; padding: 15px; border-radius: 5px; border-top: 3px solid #3b82f6;">
    <h3>📱 Responsive Design</h3>
    <p>Optimized for all device sizes from mobile to desktop</p>
  </div>
  <div style="background-color: #f0f7ff; padding: 15px; border-radius: 5px; border-top: 3px solid #3b82f6;">
    <h3>📄 PDF Upload & Analysis</h3>
    <p>Upload and analyze PDF documents with AI assistance</p>
  </div>
  <div style="background-color: #f0f7ff; padding: 15px; border-radius: 5px; border-top: 3px solid #3b82f6;">
    <h3>🎤 Voice Input</h3>
    <p>Speak to your chatbot with advanced voice recognition</p>
  </div>
  <div style="background-color: #f0f7ff; padding: 15px; border-radius: 5px; border-top: 3px solid #3b82f6;">
    <h3>🌈 Parallax Background</h3>
    <p>Engaging visual effects that respond to user interaction</p>
  </div>
  <div style="background-color: #f0f7ff; padding: 15px; border-radius: 5px; border-top: 3px solid #3b82f6;">
    <h3>🌓 Theme Toggle</h3>
    <p>Switch seamlessly between light and dark modes</p>
  </div>
  <div style="background-color: #f0f7ff; padding: 15px; border-radius: 5px; border-top: 3px solid #3b82f6;">
    <h3>📺 Full Screen Mode</h3>
    <p>Immersive chat experience without distractions</p>
  </div>
</div>

<br>

## 🌐 Live Demo

<div align="center">
  <h3>Experience ChatNova in action:</h3>
  
  <a href="https://vercel.com/sohomchatterjee07-gmailcoms-projects/v0-chatbot-ui-ux-design" style="display: inline-block; background-color: #000; color: #fff; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: bold; margin: 10px 0;">
    🚀 LAUNCH DEMO
  </a>
</div>

<hr style="background: linear-gradient(to right, #fc466b, #3f5efb); height: 2px; border: none;">

## 🛠️ Build Your App

Continue building and customizing your app on:

<div align="center">
  <a href="https://v0.dev/chat/projects/F6M4g0l0gt0" style="display: inline-block; background-color: #000; color: #fff; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: bold; margin: 10px 0;">
    ⚙️ CUSTOMIZE ON V0.DEV
  </a>
</div>

<br>

## 🔄 How It Works

<div align="center">

```mermaid
graph TD
    A[Create/Modify Project] -->|Using v0.dev| B[Deploy Chats]
    B -->|From v0 interface| C[Automatic Push]
    C -->|To this repository| D[Vercel Deployment]
    D -->|Latest version| E[Live Application]
    
    style A fill:#f9d5e5,stroke:#333,stroke-width:2px
    style B fill:#eeeeee,stroke:#333,stroke-width:2px
    style C fill:#d5f9e5,stroke:#333,stroke-width:2px
    style D fill:#e5d5f9,stroke:#333,stroke-width:2px
    style E fill:#f9e5d5,stroke:#333,stroke-width:2px
```

</div>

1. **Create and modify** your project using [v0.dev](https://v0.dev)
2. **Deploy your chats** from the v0 interface
3. **Changes are automatically pushed** to this repository
4. **Vercel deploys** the latest version from this repository

<hr style="background: linear-gradient(to right, #8e2de2, #4a00e0); height: 2px; border: none;">

## 💻 Technologies

<div align="center">

| Technology | Description | Benefits |
|------------|-------------|----------|
| ![Next.js](https://img.shields.io/badge/Next.js-000000?style=flat-square&logo=next.js&logoColor=white) | React framework for production | Server-side rendering, optimized performance |
| ![TypeScript](https://img.shields.io/badge/TypeScript-3178C6?style=flat-square&logo=typescript&logoColor=white) | Typed JavaScript | Type safety, better developer experience |
| ![Tailwind CSS](https://img.shields.io/badge/Tailwind_CSS-06B6D4?style=flat-square&logo=tailwind-css&logoColor=white) | Utility-first CSS framework | Rapid UI development, consistent design |
| ![Vercel](https://img.shields.io/badge/Vercel-000000?style=flat-square&logo=vercel&logoColor=white) | Deployment platform | Global CDN, automatic deployments |
| ![v0.dev](https://img.shields.io/badge/v0.dev-000000?style=flat-square&logo=v&logoColor=white) | AI-powered design tool | Rapid prototyping, design automation |

</div>

<br>

## 📁 Project Structure

<div style="background-color: #f8f9fa; padding: 20px; border-radius: 5px; font-family: monospace; font-size: 14px;">

```
chatbot-ui-ux-design/
├── 📂 app/                  # Next.js app directory
│   ├── 📂 api/              # API routes
│   │   ├── 📄 chat/route.ts        # Chat API endpoint
│   │   └── 📄 upload-pdf/route.ts  # PDF upload endpoint
│   ├── 📄 globals.css       # Global styles
│   ├── 📄 layout.tsx        # Root layout
│   └── 📄 page.tsx          # Home page
├── 📂 components/           # UI components
│   ├── 📂 ui/               # Reusable UI components
│   ├── 📄 chat-interface.tsx
│   ├── 📄 file-upload.tsx
│   ├── 📄 voice-input.tsx
│   └── ... (and more)
├── 📂 hooks/                # Custom React hooks
├── 📂 lib/                  # Utility functions
├── 📂 public/               # Static assets
├── 📂 styles/               # Additional styles
└── 📂 types/                # TypeScript type definitions
```

</div>

<hr style="background: linear-gradient(to right, #00b09b, #96c93d); height: 2px; border: none;">

## 🚦 Getting Started

<div style="background-color: #f0f7ff; padding: 20px; border-radius: 5px; border-left: 4px solid #3b82f6;">

```bash
# Clone the repository
git clone https://github.com/Sagexd08/ChatNova.git

# Navigate to the project directory
cd ChatNova

# Install dependencies
npm install
# or
yarn install
# or
pnpm install

# Start the development server
npm run dev
# or
yarn dev
# or
pnpm dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

</div>

<br>

## 👥 Contributing

Contributions are welcome! Feel free to open issues or submit pull requests to help improve ChatNova.

<hr style="background: linear-gradient(to right, #ff416c, #ff4b2b); height: 2px; border: none;">

<div align="center">

<img src="https://raw.githubusercontent.com/Sagexd08/ChatNova/main/public/placeholder-logo.svg" alt="ChatNova Logo" width="100px" />

**Built with ❤️ using [v0.dev](https://v0.dev)**

</div>
