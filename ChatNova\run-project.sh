#!/bin/bash

echo "========================================"
echo "ChatNova - Next-Gen AI Companion Setup"
echo "========================================"
echo

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "ERROR: Node.js is not installed or not in PATH"
    echo "Please install Node.js from https://nodejs.org/"
    echo "Recommended version: 18.x or higher"
    exit 1
fi

echo "Node.js version:"
node -v
echo

# Check if npm is available
if ! command -v npm &> /dev/null; then
    echo "ERROR: npm is not available"
    echo "Please ensure npm is installed with Node.js"
    exit 1
fi

echo "npm version:"
npm -v
echo

# Check if .env.local exists
if [ ! -f ".env.local" ]; then
    echo "WARNING: .env.local file not found"
    echo "Creating .env.local from example..."
    cp ".env.local.example" ".env.local"
    echo
    echo "IMPORTANT: Please edit .env.local and add your Gemini API key"
    echo "Get your API key from: https://makersuite.google.com/app/apikey"
    echo
    read -p "Press Enter to continue..."
fi

# Install dependencies
echo "Installing dependencies..."
npm install
if [ $? -ne 0 ]; then
    echo "ERROR: Failed to install dependencies"
    exit 1
fi

echo
echo "========================================"
echo "Setup completed successfully!"
echo "========================================"
echo
echo "Starting development server..."
echo "Open http://localhost:3000 in your browser"
echo "Press Ctrl+C to stop the server"
echo

# Start the development server
npm run dev
