"use client"

import { useEffect, useRef } from "react"
import { useTheme } from "next-themes"

interface ParallaxBackgroundProps {
  mousePosition: { x: number; y: number }
  scrollPosition: number
}

export function ParallaxBackground({ mousePosition, scrollPosition }: ParallaxBackgroundProps) {
  const { theme } = useTheme()
  const isDark = theme === "dark"

  // References to background elements for direct manipulation
  const blob1Ref = useRef<HTMLDivElement>(null)
  const blob2Ref = useRef<HTMLDivElement>(null)
  const blob3Ref = useRef<HTMLDivElement>(null)
  const blob4Ref = useRef<HTMLDivElement>(null)
  const starsRef = useRef<HTMLDivElement>(null)
  const glowRef = useRef<HTMLDivElement>(null)

  // Apply 3D transforms based on mouse position and scroll
  useEffect(() => {
    try {
      if (blob1Ref.current) {
        blob1Ref.current.style.transform = `translate3d(${mousePosition.x * -40}px, ${
          mousePosition.y * -40 - scrollPosition * 0.07
        }px, ${mousePosition.x * 50}px) rotateX(${mousePosition.y * 12}deg) rotateY(${mousePosition.x * -12}deg)`
      }

      if (blob2Ref.current) {
        blob2Ref.current.style.transform = `translate3d(${mousePosition.x * 30}px, ${
          mousePosition.y * 30 - scrollPosition * 0.05
        }px, ${mousePosition.y * -40}px) rotateX(${mousePosition.y * -10}deg) rotateY(${mousePosition.x * 10}deg)`
      }

      if (blob3Ref.current) {
        blob3Ref.current.style.transform = `translate3d(${mousePosition.x * -25}px, ${
          mousePosition.y * -25 - scrollPosition * 0.09
        }px, ${mousePosition.x * -30}px) rotateX(${mousePosition.y * 8}deg) rotateY(${mousePosition.x * -8}deg)`
      }
      
      if (blob4Ref.current) {
        blob4Ref.current.style.transform = `translate3d(${mousePosition.x * 20}px, ${
          mousePosition.y * -20 - scrollPosition * 0.06
        }px, ${mousePosition.x * 25}px) rotateX(${mousePosition.y * -6}deg) rotateY(${mousePosition.x * 6}deg)`
      }

      if (starsRef.current) {
        starsRef.current.style.transform = `translate3d(${mousePosition.x * -8}px, ${
          mousePosition.y * -8 - scrollPosition * 0.03
        }px, 0)`
      }
      
      if (glowRef.current) {
        glowRef.current.style.transform = `translate3d(${mousePosition.x * 15}px, ${
          mousePosition.y * 15 - scrollPosition * 0.02
        }px, 0)`
        
        // Dynamic glow position following mouse
        const x = 50 + mousePosition.x * 20
        const y = 50 + mousePosition.y * 20
        glowRef.current.style.background = isDark 
          ? `radial-gradient(circle at ${x}% ${y}%, rgba(139, 92, 246, 0.15), transparent 60%)`
          : `radial-gradient(circle at ${x}% ${y}%, rgba(139, 92, 246, 0.1), transparent 60%)`
      }
    } catch (error) {
      // Silently handle any transform errors
    }
  }, [mousePosition, scrollPosition, isDark])

  return (
    <div className="fixed inset-0 overflow-hidden pointer-events-none">
      {/* Main gradient background */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-purple-900 transition-colors duration-500"></div>

      {/* Dynamic glow effect */}
      <div
        ref={glowRef}
        className="absolute inset-0 opacity-80 transition-all duration-300"
      ></div>

      {/* Stars effect (enhanced for dark mode) */}
      <div
        ref={starsRef}
        className="absolute inset-0 transition-opacity duration-500"
        style={{
          opacity: isDark ? 0.8 : 0,
          backgroundImage: `
            radial-gradient(1.5px 1.5px at 20px 30px, white, rgba(0,0,0,0)), 
            radial-gradient(1.5px 1.5px at 40px 70px, white, rgba(0,0,0,0)), 
            radial-gradient(1px 1px at 90px 40px, white, rgba(0,0,0,0)),
            radial-gradient(2px 2px at 130px 80px, white, rgba(0,0,0,0)), 
            radial-gradient(1.5px 1.5px at 160px 30px, white, rgba(0,0,0,0)),
            radial-gradient(1px 1px at 200px 60px, white, rgba(0,0,0,0)),
            radial-gradient(1.5px 1.5px at 180px 100px, white, rgba(0,0,0,0)),
            radial-gradient(1px 1px at 250px 50px, white, rgba(0,0,0,0))
          `,
          backgroundRepeat: "repeat",
          backgroundSize: "250px 150px",
        }}
      ></div>

      {/* 3D Animated blobs with parallax effect */}
      <div
        ref={blob1Ref}
        className="absolute -top-40 -right-40 w-96 h-96 bg-purple-300 dark:bg-purple-600 rounded-full mix-blend-multiply dark:mix-blend-normal filter blur-xl opacity-30 dark:opacity-25 animate-blob will-change-transform"
        style={{ transformStyle: "preserve-3d" }}
      ></div>

      <div
        ref={blob2Ref}
        className="absolute -bottom-40 -left-40 w-96 h-96 bg-blue-300 dark:bg-blue-600 rounded-full mix-blend-multiply dark:mix-blend-normal filter blur-xl opacity-30 dark:opacity-25 animate-blob animation-delay-2000 will-change-transform"
        style={{ transformStyle: "preserve-3d" }}
      ></div>

      <div
        ref={blob3Ref}
        className="absolute top-40 left-40 w-96 h-96 bg-pink-300 dark:bg-pink-600 rounded-full mix-blend-multiply dark:mix-blend-normal filter blur-xl opacity-30 dark:opacity-25 animate-blob animation-delay-4000 will-change-transform"
        style={{ transformStyle: "preserve-3d" }}
      ></div>
      
      <div
        ref={blob4Ref}
        className="absolute bottom-60 right-60 w-80 h-80 bg-indigo-300 dark:bg-indigo-600 rounded-full mix-blend-multiply dark:mix-blend-normal filter blur-xl opacity-30 dark:opacity-25 animate-blob animation-delay-6000 will-change-transform"
        style={{ transformStyle: "preserve-3d" }}
      ></div>
      
      {/* Light grid overlay */}
      <div 
        className="absolute inset-0 bg-grid-pattern opacity-5 dark:opacity-10"
        style={{
          backgroundImage: isDark 
            ? 'linear-gradient(to right, rgba(255,255,255,0.05) 1px, transparent 1px), linear-gradient(to bottom, rgba(255,255,255,0.05) 1px, transparent 1px)'
            : 'linear-gradient(to right, rgba(0,0,0,0.05) 1px, transparent 1px), linear-gradient(to bottom, rgba(0,0,0,0.05) 1px, transparent 1px)',
          backgroundSize: '40px 40px'
        }}
      ></div>
    </div>
  )
}
