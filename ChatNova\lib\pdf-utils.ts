import * as pdfjsLib from 'pdfjs-dist'

// Configure PDF.js worker
if (typeof window !== 'undefined') {
  pdfjsLib.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjsLib.version}/pdf.worker.min.js`
}

export interface PDFExtractionResult {
  text: string
  pageCount: number
  metadata?: {
    title?: string
    author?: string
    subject?: string
    creator?: string
    producer?: string
    creationDate?: Date
    modificationDate?: Date
  }
}

export async function extractPDFText(file: File): Promise<string> {
  try {
    const arrayBuffer = await file.arrayBuffer()
    const pdf = await pdfjsLib.getDocument({ data: arrayBuffer }).promise
    
    let fullText = ''
    
    // Extract text from each page
    for (let pageNum = 1; pageNum <= pdf.numPages; pageNum++) {
      const page = await pdf.getPage(pageNum)
      const textContent = await page.getTextContent()
      
      // Combine text items with proper spacing
      const pageText = textContent.items
        .map((item: any) => {
          if ('str' in item) {
            return item.str
          }
          return ''
        })
        .join(' ')
        .replace(/\s+/g, ' ') // Normalize whitespace
        .trim()
      
      if (pageText) {
        fullText += `\n\n--- Page ${pageNum} ---\n${pageText}`
      }
    }
    
    return fullText.trim()
  } catch (error) {
    console.error('Error extracting PDF text:', error)
    throw new Error('Failed to extract text from PDF. The file may be corrupted or password-protected.')
  }
}

export async function extractPDFMetadata(file: File): Promise<PDFExtractionResult> {
  try {
    const arrayBuffer = await file.arrayBuffer()
    const pdf = await pdfjsLib.getDocument({ data: arrayBuffer }).promise
    
    // Extract metadata
    const metadata = await pdf.getMetadata()
    const info = metadata.info
    
    // Extract text
    const text = await extractPDFText(file)
    
    return {
      text,
      pageCount: pdf.numPages,
      metadata: {
        title: info.Title || undefined,
        author: info.Author || undefined,
        subject: info.Subject || undefined,
        creator: info.Creator || undefined,
        producer: info.Producer || undefined,
        creationDate: info.CreationDate ? new Date(info.CreationDate) : undefined,
        modificationDate: info.ModDate ? new Date(info.ModDate) : undefined,
      }
    }
  } catch (error) {
    console.error('Error extracting PDF metadata:', error)
    throw new Error('Failed to process PDF file.')
  }
}

export function validatePDFFile(file: File): { isValid: boolean; error?: string } {
  // Check file type
  if (file.type !== 'application/pdf') {
    return {
      isValid: false,
      error: 'File must be a PDF document'
    }
  }
  
  // Check file size (10MB limit)
  const maxSize = 10 * 1024 * 1024 // 10MB
  if (file.size > maxSize) {
    return {
      isValid: false,
      error: 'File size must be less than 10MB'
    }
  }
  
  // Check file name
  if (!file.name.toLowerCase().endsWith('.pdf')) {
    return {
      isValid: false,
      error: 'File must have a .pdf extension'
    }
  }
  
  return { isValid: true }
}

export function summarizeText(text: string, maxLength: number = 500): string {
  if (text.length <= maxLength) {
    return text
  }
  
  // Try to break at sentence boundaries
  const sentences = text.split(/[.!?]+/)
  let summary = ''
  
  for (const sentence of sentences) {
    if (summary.length + sentence.length + 1 <= maxLength) {
      summary += sentence.trim() + '. '
    } else {
      break
    }
  }
  
  // If no complete sentences fit, truncate at word boundary
  if (summary.length === 0) {
    const words = text.split(' ')
    for (const word of words) {
      if (summary.length + word.length + 1 <= maxLength) {
        summary += word + ' '
      } else {
        break
      }
    }
    summary += '...'
  }
  
  return summary.trim()
}

export function extractKeywords(text: string, count: number = 10): string[] {
  // Simple keyword extraction based on word frequency
  const words = text
    .toLowerCase()
    .replace(/[^\w\s]/g, ' ')
    .split(/\s+/)
    .filter(word => word.length > 3) // Filter out short words
  
  // Common stop words to exclude
  const stopWords = new Set([
    'the', 'and', 'for', 'are', 'but', 'not', 'you', 'all', 'can', 'had', 'her', 'was', 'one', 'our', 'out', 'day', 'get', 'has', 'him', 'his', 'how', 'its', 'may', 'new', 'now', 'old', 'see', 'two', 'who', 'boy', 'did', 'she', 'use', 'way', 'will', 'with', 'this', 'that', 'they', 'have', 'from', 'been', 'said', 'each', 'which', 'their', 'time', 'would', 'there', 'could', 'other', 'after', 'first', 'well', 'water', 'were', 'many', 'some', 'what', 'these', 'come', 'into', 'than', 'them', 'part', 'long', 'made', 'over', 'such', 'take', 'only', 'think', 'know', 'just', 'also', 'back', 'good', 'life', 'work', 'much', 'where', 'right', 'still', 'should', 'those', 'being', 'same', 'through', 'before', 'here', 'when', 'very', 'more', 'most', 'about', 'like', 'even', 'make', 'people', 'little', 'down', 'year', 'years', 'because', 'between', 'during', 'without', 'another', 'against', 'under', 'while', 'never', 'again', 'something', 'every', 'around', 'although', 'however', 'therefore', 'moreover', 'furthermore', 'nevertheless', 'nonetheless', 'meanwhile', 'otherwise', 'instead', 'besides', 'indeed', 'certainly', 'perhaps', 'probably', 'possibly', 'definitely', 'absolutely', 'completely', 'entirely', 'exactly', 'particularly', 'especially', 'specifically', 'generally', 'usually', 'normally', 'typically', 'basically', 'essentially', 'actually', 'really', 'quite', 'rather', 'fairly', 'pretty', 'somewhat', 'slightly', 'extremely', 'highly', 'very'
  ])
  
  // Count word frequencies
  const wordCount = new Map<string, number>()
  words.forEach(word => {
    if (!stopWords.has(word)) {
      wordCount.set(word, (wordCount.get(word) || 0) + 1)
    }
  })
  
  // Sort by frequency and return top keywords
  return Array.from(wordCount.entries())
    .sort((a, b) => b[1] - a[1])
    .slice(0, count)
    .map(([word]) => word)
}

export function chunkText(text: string, maxChunkSize: number = 4000): string[] {
  if (text.length <= maxChunkSize) {
    return [text]
  }
  
  const chunks: string[] = []
  const paragraphs = text.split('\n\n')
  let currentChunk = ''
  
  for (const paragraph of paragraphs) {
    if (currentChunk.length + paragraph.length + 2 <= maxChunkSize) {
      currentChunk += (currentChunk ? '\n\n' : '') + paragraph
    } else {
      if (currentChunk) {
        chunks.push(currentChunk)
        currentChunk = paragraph
      } else {
        // Paragraph is too long, split by sentences
        const sentences = paragraph.split(/[.!?]+/)
        for (const sentence of sentences) {
          if (currentChunk.length + sentence.length + 1 <= maxChunkSize) {
            currentChunk += (currentChunk ? '. ' : '') + sentence.trim()
          } else {
            if (currentChunk) {
              chunks.push(currentChunk + '.')
              currentChunk = sentence.trim()
            } else {
              // Sentence is too long, force split
              chunks.push(sentence.trim().substring(0, maxChunkSize))
              currentChunk = sentence.trim().substring(maxChunkSize)
            }
          }
        }
      }
    }
  }
  
  if (currentChunk) {
    chunks.push(currentChunk)
  }
  
  return chunks
}
