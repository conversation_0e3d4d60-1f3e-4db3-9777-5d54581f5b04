"use client"

import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { FileText, X } from "lucide-react"

interface UploadedFilesProps {
  files: Array<{ name: string; content: string; type: string }>
  onRemoveFile: (index: number) => void
}

export function UploadedFiles({ files, onRemoveFile }: UploadedFilesProps) {
  return (
    <Card className="border-blue-200 bg-blue-50">
      <CardContent className="p-4">
        <h3 className="text-sm font-medium text-blue-800 mb-3">Uploaded Documents</h3>
        <div className="space-y-2">
          {files.map((file, index) => (
            <div key={index} className="flex items-center justify-between bg-white rounded-md p-2 border">
              <div className="flex items-center gap-2">
                <FileText className="w-4 h-4 text-blue-600" />
                <span className="text-sm text-gray-700 truncate">{file.name}</span>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onRemoveFile(index)}
                className="h-6 w-6 p-0 text-gray-500 hover:text-red-600"
              >
                <X className="w-3 h-3" />
              </Button>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
