# ChatNova Setup Instructions

## Prerequisites

Before running ChatNova, ensure you have the following installed:

1. **Node.js 18 or higher** - [Download from nodejs.org](https://nodejs.org/)
2. **npm or yarn** - Comes with Node.js
3. **Google Gemini API Key** - [Get one here](https://makersuite.google.com/app/apikey)

## Step-by-Step Setup

### 1. Install Dependencies

Open a terminal in the ChatNova directory and run:

```bash
npm install
```

This will install all required dependencies including:
- Next.js 14 with TypeScript
- Tailwind CSS for styling
- Framer Motion for animations
- Radix UI components
- Google Generative AI SDK
- PDF.js for document processing
- React Dropzone for file uploads
- Zustand for state management

### 2. Configure Environment Variables

1. Copy the example environment file:
   ```bash
   cp .env.local .env.local.example
   ```

2. Edit `.env.local` and add your Gemini API key:
   ```env
   GEMINI_API_KEY=your_actual_gemini_api_key_here
   NEXT_PUBLIC_APP_NAME=ChatNova
   NEXT_PUBLIC_APP_VERSION=1.0.0
   ```

### 3. Start the Development Server

```bash
npm run dev
```

The application will start on [http://localhost:3000](http://localhost:3000)

### 4. Build for Production

To create a production build:

```bash
npm run build
npm start
```

## Getting Your Gemini API Key

1. Go to [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Sign in with your Google account
3. Click "Create API Key"
4. Copy the generated key
5. Add it to your `.env.local` file

## Troubleshooting

### Common Issues

1. **"Module not found" errors**
   - Run `npm install` to ensure all dependencies are installed
   - Delete `node_modules` and `package-lock.json`, then run `npm install` again

2. **API key not working**
   - Ensure your Gemini API key is correctly set in `.env.local`
   - Check that the key has proper permissions
   - Restart the development server after changing environment variables

3. **PDF upload not working**
   - Check browser console for errors
   - Ensure PDF files are under 10MB
   - Try with different PDF files

4. **Voice features not working**
   - Ensure you're using HTTPS (required for microphone access)
   - Check browser permissions for microphone access
   - Voice features require a modern browser with Web Speech API support

### Browser Compatibility

ChatNova works best with modern browsers:
- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

### Performance Tips

1. **For better performance:**
   - Use a fast internet connection for AI responses
   - Keep PDF files under 5MB for faster processing
   - Close unnecessary browser tabs

2. **For development:**
   - Use `npm run dev` for hot reloading
   - Enable React Developer Tools for debugging
   - Use browser DevTools for performance monitoring

## Features Overview

Once running, you can:

1. **Chat with AI**: Start conversations with the empathetic AI companion
2. **Upload PDFs**: Drag and drop PDF files for analysis
3. **Voice Interaction**: Enable voice input/output in settings
4. **Theme Toggle**: Switch between dark and light modes
5. **Session Management**: Create and manage multiple chat sessions

## Next Steps

After setup, explore:
- Customizing the AI personality in `stores/chat-store.ts`
- Modifying themes in `tailwind.config.js`
- Adding new features in the `components/` directory
- Extending API functionality in `app/api/`

## Support

If you encounter issues:
1. Check the browser console for error messages
2. Verify all environment variables are set correctly
3. Ensure your Gemini API key is valid and has quota
4. Try refreshing the page or restarting the development server

For additional help, refer to the main README.md file or create an issue in the project repository.
