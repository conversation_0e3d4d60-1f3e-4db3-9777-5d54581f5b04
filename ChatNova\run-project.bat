@echo off
echo ========================================
echo ChatNova - Next-Gen AI Companion Setup
echo ========================================
echo.

REM Check if Node.js is installed
node -v >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org/
    echo Recommended version: 18.x or higher
    pause
    exit /b 1
)

echo Node.js version:
node -v
echo.

REM Check if npm is available
npm -v >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: npm is not available
    echo Please ensure npm is installed with Node.js
    pause
    exit /b 1
)

echo npm version:
npm -v
echo.

REM Check if .env.local exists
if not exist ".env.local" (
    echo WARNING: .env.local file not found
    echo Creating .env.local from example...
    copy ".env.local.example" ".env.local"
    echo.
    echo IMPORTANT: Please edit .env.local and add your Gemini API key
    echo Get your API key from: https://makersuite.google.com/app/apikey
    echo.
    pause
)

REM Install dependencies
echo Installing dependencies...
npm install
if %errorlevel% neq 0 (
    echo ERROR: Failed to install dependencies
    pause
    exit /b 1
)

echo.
echo ========================================
echo Setup completed successfully!
echo ========================================
echo.
echo Starting development server...
echo Open http://localhost:3000 in your browser
echo Press Ctrl+C to stop the server
echo.

REM Start the development server
npm run dev
