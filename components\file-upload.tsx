"use client"

import { useState, useCallback } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Upload, FileText, AlertCircle, CheckCircle } from "lucide-react"
import { useDropzone } from "react-dropzone"

interface FileUploadProps {
  onFileUpload: (files: Array<{ name: string; content: string; type: string }>) => void
}

export function FileUpload({ onFileUpload }: FileUploadProps) {
  const [isUploading, setIsUploading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [uploadSuccess, setUploadSuccess] = useState(false)

  const onDrop = useCallback(
    async (acceptedFiles: File[]) => {
      setIsUploading(true)
      setError(null)
      setUploadSuccess(false)

      try {
        const uploadPromises = acceptedFiles.map(async (file) => {
          const formData = new FormData()
          formData.append("file", file)

          const response = await fetch("/api/upload-pdf", {
            method: "POST",
            body: formData,
          })

          if (!response.ok) {
            throw new Error(`Failed to process ${file.name}`)
          }

          const result = await response.json()
          return {
            name: file.name,
            content: result.content,
            type: file.type,
          }
        })

        const processedFiles = await Promise.all(uploadPromises)

        // Show success message briefly before closing
        setUploadSuccess(true)

        // Wait a moment to show success message, then trigger the callback
        setTimeout(() => {
          onFileUpload(processedFiles)
        }, 1000)
      } catch (err) {
        setError(err instanceof Error ? err.message : "Failed to upload files")
      } finally {
        setIsUploading(false)
      }
    },
    [onFileUpload],
  )

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      "application/pdf": [".pdf"],
    },
    maxFiles: 5,
    maxSize: 10 * 1024 * 1024, // 10MB
  })

  return (
    <Card className="border-2 border-dashed border-gray-300 hover:border-blue-400 transition-colors">
      <CardContent className="p-6">
        <div
          {...getRootProps()}
          className={`text-center cursor-pointer ${isDragActive ? "text-blue-600" : "text-gray-600"}`}
        >
          <input {...getInputProps()} />
          <div className="space-y-4">
            <div className="mx-auto w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
              {isUploading ? (
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
              ) : uploadSuccess ? (
                <CheckCircle className="w-6 h-6 text-green-600" />
              ) : (
                <Upload className="w-6 h-6 text-blue-600" />
              )}
            </div>

            <div>
              {uploadSuccess ? (
                <p className="text-lg font-medium text-green-600">Upload successful!</p>
              ) : (
                <>
                  <p className="text-lg font-medium">{isDragActive ? "Drop your PDFs here" : "Upload PDF Documents"}</p>
                  <p className="text-sm text-gray-500 mt-1">Drag and drop or click to select files (Max 10MB each)</p>
                </>
              )}
            </div>

            {!isUploading && !uploadSuccess && (
              <Button variant="outline" className="mt-4">
                <FileText className="w-4 h-4 mr-2" />
                Choose Files
              </Button>
            )}
          </div>
        </div>

        {error && (
          <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-md flex items-center gap-2 text-red-700">
            <AlertCircle className="w-4 h-4" />
            <span className="text-sm">{error}</span>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
