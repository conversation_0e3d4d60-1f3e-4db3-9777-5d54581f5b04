'use client'

import { motion } from 'framer-motion'
import { Button } from '@/components/ui/button'
import { 
  Menu, 
  Sparkles, 
  Sun, 
  Moon, 
  Settings,
  Plus,
  Mic,
  MicOff
} from 'lucide-react'
import { useTheme } from 'next-themes'
import { useChatStore } from '@/stores/chat-store'
import { useState, useEffect } from 'react'

interface HeaderProps {
  onMenuClick: () => void
  sidebarOpen: boolean
}

export function Header({ onMenuClick, sidebarOpen }: HeaderProps) {
  const { theme, setTheme } = useTheme()
  const { createSession, personality, voiceEnabled, setVoiceEnabled } = useChatStore()
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  const handleNewChat = () => {
    createSession()
  }

  const toggleTheme = () => {
    setTheme(theme === 'dark' ? 'light' : 'dark')
  }

  const toggleVoice = () => {
    setVoiceEnabled(!voiceEnabled)
  }

  if (!mounted) {
    return null
  }

  return (
    <motion.header
      initial={{ y: -100, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ duration: 0.5, ease: "easeOut" }}
      className="glass-effect border-b border-border/50 px-4 py-3 flex items-center justify-between sticky top-0 z-40 backdrop-blur-xl"
    >
      {/* Left Section */}
      <div className="flex items-center space-x-4">
        <Button
          variant="ghost"
          size="icon"
          onClick={onMenuClick}
          className="lg:hidden hover:bg-white/10"
        >
          <Menu className="w-5 h-5" />
        </Button>

        <motion.div
          whileHover={{ scale: 1.05 }}
          className="flex items-center space-x-3"
        >
          <div className="relative">
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
              className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center"
            >
              <Sparkles className="w-4 h-4 text-white" />
            </motion.div>
            <div className="absolute inset-0 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 opacity-20 animate-ping" />
          </div>
          
          <div className="hidden sm:block">
            <h1 className="text-xl font-bold gradient-text">ChatNova</h1>
            <p className="text-xs text-muted-foreground">
              with {personality.name} {personality.avatar}
            </p>
          </div>
        </motion.div>
      </div>

      {/* Center Section - Status */}
      <div className="hidden md:flex items-center space-x-2">
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ delay: 0.3 }}
          className="flex items-center space-x-2 px-3 py-1 rounded-full bg-green-500/10 border border-green-500/20"
        >
          <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
          <span className="text-xs text-green-600 dark:text-green-400 font-medium">
            AI Online
          </span>
        </motion.div>
      </div>

      {/* Right Section */}
      <div className="flex items-center space-x-2">
        {/* Voice Toggle */}
        <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
          <Button
            variant="ghost"
            size="icon"
            onClick={toggleVoice}
            className={`hover:bg-white/10 ${voiceEnabled ? 'text-green-500' : 'text-muted-foreground'}`}
            title={voiceEnabled ? 'Voice enabled' : 'Voice disabled'}
          >
            {voiceEnabled ? (
              <Mic className="w-4 h-4" />
            ) : (
              <MicOff className="w-4 h-4" />
            )}
          </Button>
        </motion.div>

        {/* New Chat */}
        <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
          <Button
            variant="ghost"
            size="icon"
            onClick={handleNewChat}
            className="hover:bg-white/10"
            title="New chat"
          >
            <Plus className="w-4 h-4" />
          </Button>
        </motion.div>

        {/* Theme Toggle */}
        <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
          <Button
            variant="ghost"
            size="icon"
            onClick={toggleTheme}
            className="hover:bg-white/10"
            title="Toggle theme"
          >
            {theme === 'dark' ? (
              <Sun className="w-4 h-4" />
            ) : (
              <Moon className="w-4 h-4" />
            )}
          </Button>
        </motion.div>

        {/* Settings */}
        <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
          <Button
            variant="ghost"
            size="icon"
            className="hover:bg-white/10"
            title="Settings"
          >
            <Settings className="w-4 h-4" />
          </Button>
        </motion.div>
      </div>
    </motion.header>
  )
}
