import { create } from 'zustand'
import { persist } from 'zustand/middleware'

export interface Message {
  id: string
  content: string
  role: 'user' | 'assistant'
  timestamp: Date
  type?: 'text' | 'pdf' | 'image'
  metadata?: {
    fileName?: string
    fileSize?: number
    extractedText?: string
    processingTime?: number
  }
}

export interface ChatSession {
  id: string
  title: string
  messages: Message[]
  createdAt: Date
  updatedAt: Date
}

export interface ChatbotPersonality {
  name: string
  tone: 'professional' | 'friendly' | 'casual' | 'empathetic' | 'enthusiastic'
  avatar: string
  description: string
}

interface ChatState {
  // Current session
  currentSessionId: string | null
  messages: Message[]
  isLoading: boolean
  isTyping: boolean
  
  // Sessions management
  sessions: ChatSession[]
  
  // Settings
  isFirstVisit: boolean
  personality: ChatbotPersonality
  voiceEnabled: boolean
  autoSpeak: boolean
  language: string
  
  // PDF processing
  uploadedFiles: File[]
  extractedContent: string[]
  
  // Actions
  addMessage: (message: Omit<Message, 'id' | 'timestamp'>) => void
  updateMessage: (id: string, updates: Partial<Message>) => void
  deleteMessage: (id: string) => void
  clearMessages: () => void
  
  // Session management
  createSession: (title?: string) => string
  loadSession: (sessionId: string) => void
  deleteSession: (sessionId: string) => void
  updateSessionTitle: (sessionId: string, title: string) => void
  
  // Settings
  setFirstVisit: (value: boolean) => void
  setPersonality: (personality: ChatbotPersonality) => void
  setVoiceEnabled: (enabled: boolean) => void
  setAutoSpeak: (enabled: boolean) => void
  setLanguage: (language: string) => void
  
  // Loading states
  setLoading: (loading: boolean) => void
  setTyping: (typing: boolean) => void
  
  // PDF handling
  addUploadedFile: (file: File) => void
  removeUploadedFile: (fileName: string) => void
  addExtractedContent: (content: string) => void
  clearExtractedContent: () => void
}

const defaultPersonality: ChatbotPersonality = {
  name: 'Nova',
  tone: 'empathetic',
  avatar: '🤖',
  description: 'Your intelligent and empathetic AI companion'
}

export const useChatStore = create<ChatState>()(
  persist(
    (set, get) => ({
      // Initial state
      currentSessionId: null,
      messages: [],
      isLoading: false,
      isTyping: false,
      sessions: [],
      isFirstVisit: true,
      personality: defaultPersonality,
      voiceEnabled: false,
      autoSpeak: false,
      language: 'en-US',
      uploadedFiles: [],
      extractedContent: [],

      // Message actions
      addMessage: (message) => {
        const newMessage: Message = {
          ...message,
          id: Math.random().toString(36).substr(2, 9),
          timestamp: new Date(),
        }
        
        set((state) => {
          const updatedMessages = [...state.messages, newMessage]
          
          // Update current session
          if (state.currentSessionId) {
            const updatedSessions = state.sessions.map(session =>
              session.id === state.currentSessionId
                ? { ...session, messages: updatedMessages, updatedAt: new Date() }
                : session
            )
            return {
              messages: updatedMessages,
              sessions: updatedSessions,
            }
          }
          
          return { messages: updatedMessages }
        })
      },

      updateMessage: (id, updates) => {
        set((state) => {
          const updatedMessages = state.messages.map(msg =>
            msg.id === id ? { ...msg, ...updates } : msg
          )
          
          // Update current session
          if (state.currentSessionId) {
            const updatedSessions = state.sessions.map(session =>
              session.id === state.currentSessionId
                ? { ...session, messages: updatedMessages, updatedAt: new Date() }
                : session
            )
            return {
              messages: updatedMessages,
              sessions: updatedSessions,
            }
          }
          
          return { messages: updatedMessages }
        })
      },

      deleteMessage: (id) => {
        set((state) => {
          const updatedMessages = state.messages.filter(msg => msg.id !== id)
          
          // Update current session
          if (state.currentSessionId) {
            const updatedSessions = state.sessions.map(session =>
              session.id === state.currentSessionId
                ? { ...session, messages: updatedMessages, updatedAt: new Date() }
                : session
            )
            return {
              messages: updatedMessages,
              sessions: updatedSessions,
            }
          }
          
          return { messages: updatedMessages }
        })
      },

      clearMessages: () => {
        set({ messages: [] })
      },

      // Session management
      createSession: (title) => {
        const sessionId = Math.random().toString(36).substr(2, 9)
        const now = new Date()
        
        const newSession: ChatSession = {
          id: sessionId,
          title: title || `Chat ${get().sessions.length + 1}`,
          messages: [],
          createdAt: now,
          updatedAt: now,
        }
        
        set((state) => ({
          currentSessionId: sessionId,
          messages: [],
          sessions: [...state.sessions, newSession],
        }))
        
        return sessionId
      },

      loadSession: (sessionId) => {
        const session = get().sessions.find(s => s.id === sessionId)
        if (session) {
          set({
            currentSessionId: sessionId,
            messages: session.messages,
          })
        }
      },

      deleteSession: (sessionId) => {
        set((state) => {
          const updatedSessions = state.sessions.filter(s => s.id !== sessionId)
          const newCurrentId = state.currentSessionId === sessionId ? null : state.currentSessionId
          
          return {
            sessions: updatedSessions,
            currentSessionId: newCurrentId,
            messages: newCurrentId ? state.messages : [],
          }
        })
      },

      updateSessionTitle: (sessionId, title) => {
        set((state) => ({
          sessions: state.sessions.map(session =>
            session.id === sessionId
              ? { ...session, title, updatedAt: new Date() }
              : session
          ),
        }))
      },

      // Settings
      setFirstVisit: (value) => set({ isFirstVisit: value }),
      setPersonality: (personality) => set({ personality }),
      setVoiceEnabled: (enabled) => set({ voiceEnabled: enabled }),
      setAutoSpeak: (enabled) => set({ autoSpeak: enabled }),
      setLanguage: (language) => set({ language }),

      // Loading states
      setLoading: (loading) => set({ isLoading: loading }),
      setTyping: (typing) => set({ isTyping: typing }),

      // PDF handling
      addUploadedFile: (file) => {
        set((state) => ({
          uploadedFiles: [...state.uploadedFiles, file],
        }))
      },

      removeUploadedFile: (fileName) => {
        set((state) => ({
          uploadedFiles: state.uploadedFiles.filter(file => file.name !== fileName),
        }))
      },

      addExtractedContent: (content) => {
        set((state) => ({
          extractedContent: [...state.extractedContent, content],
        }))
      },

      clearExtractedContent: () => set({ extractedContent: [] }),
    }),
    {
      name: 'chatnova-storage',
      partialize: (state) => ({
        sessions: state.sessions,
        personality: state.personality,
        voiceEnabled: state.voiceEnabled,
        autoSpeak: state.autoSpeak,
        language: state.language,
        isFirstVisit: state.isFirstVisit,
      }),
    }
  )
)
