// Simple test script to validate Gemini API configuration
// Run with: node test-api.js

const fs = require('fs');
const path = require('path');

console.log('🧪 ChatNova API Configuration Test\n');

// Check if .env.local exists
const envPath = path.join(__dirname, '.env.local');
if (!fs.existsSync(envPath)) {
    console.error('❌ .env.local file not found!');
    console.log('Please ensure .env.local exists in the ChatNova directory');
    process.exit(1);
}

// Read and parse .env.local
const envContent = fs.readFileSync(envPath, 'utf8');
const envLines = envContent.split('\n').filter(line => line.trim() && !line.startsWith('#'));
const envVars = {};

envLines.forEach(line => {
    const [key, ...valueParts] = line.split('=');
    if (key && valueParts.length > 0) {
        envVars[key.trim()] = valueParts.join('=').trim();
    }
});

// Check API key
if (!envVars.GEMINI_API_KEY) {
    console.error('❌ GEMINI_API_KEY not found in .env.local');
    process.exit(1);
}

const apiKey = envVars.GEMINI_API_KEY;
console.log('✅ .env.local file found');
console.log('✅ GEMINI_API_KEY configured');
console.log(`📝 API Key: ${apiKey.substring(0, 10)}...${apiKey.substring(apiKey.length - 4)}`);

// Validate API key format
if (!apiKey.startsWith('AIza') || apiKey.length < 30) {
    console.warn('⚠️  API key format looks unusual');
    console.log('Expected format: AIzaSy... (39 characters)');
} else {
    console.log('✅ API key format looks correct');
}

// Check other environment variables
if (envVars.NEXT_PUBLIC_APP_NAME) {
    console.log(`✅ App Name: ${envVars.NEXT_PUBLIC_APP_NAME}`);
}
if (envVars.NEXT_PUBLIC_APP_VERSION) {
    console.log(`✅ App Version: ${envVars.NEXT_PUBLIC_APP_VERSION}`);
}

console.log('\n🎯 Configuration Summary:');
console.log('- Environment file: ✅ Ready');
console.log('- API key: ✅ Configured');
console.log('- Format: ✅ Valid');

console.log('\n🚀 Next Steps:');
console.log('1. Run: npm install');
console.log('2. Run: npm run dev');
console.log('3. Open: http://localhost:3000');

console.log('\n✨ ChatNova is ready to run!');

// Test if we can import the Gemini library (if node_modules exists)
try {
    const { GoogleGenerativeAI } = require('@google/generative-ai');
    console.log('✅ Gemini AI library available');
    
    // Quick API test (commented out to avoid quota usage)
    /*
    const genAI = new GoogleGenerativeAI(apiKey);
    const model = genAI.getGenerativeModel({ model: 'gemini-pro' });
    
    console.log('🧪 Testing API connection...');
    model.generateContent('Hello, this is a test. Please respond with "API test successful".')
        .then(result => {
            const response = result.response;
            const text = response.text();
            console.log('✅ API Test Result:', text);
        })
        .catch(error => {
            console.error('❌ API Test Failed:', error.message);
        });
    */
} catch (error) {
    console.log('ℹ️  Gemini AI library not installed yet (run npm install first)');
}
