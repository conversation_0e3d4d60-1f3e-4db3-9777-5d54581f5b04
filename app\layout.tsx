import type React from "react"
import type { <PERSON><PERSON><PERSON> } from "next"
import { Inter } from "next/font/google"
import "./globals.css"
import { ThemeProvider } from "@/components/theme-provider"
import Script from "next/script"

const inter = Inter({ subsets: ["latin"] })

export const metadata: Metadata = {
  title: "ChatNova - AI Assistant",
  description: "Advanced AI chatbot with document analysis powered by Google Gemini",
    generator: 'v0.dev'
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <Script id="suppress-extension-errors" strategy="beforeInteractive">
          {`
            // Ultra-comprehensive error suppression for browser extensions
            (function() {
              // Extensive list of extension-related keywords
              const extensionKeywords = [
                'MetaMask', 'ChromeTransport', 'extension not found', 
                'chrome-extension', 'moz-extension', 'safari-extension',
                'wallet', 'web3', 'ethereum', 'crypto', 'blockchain',
                'connectChrome', 'extension', 'addon', 'browser-extension',
                'metamask', 'coinbase', 'trustwallet', 'phantom',
                'solflare', 'keplr', 'leap', 'cosmostation'
              ];
              
              function shouldSuppress(message) {
                if (typeof message !== 'string') return false;
                const lowerMessage = message.toLowerCase();
                return extensionKeywords.some(keyword => 
                  lowerMessage.includes(keyword.toLowerCase())
                );
              }
              
              // Override all console methods
              const originalError = console.error;
              const originalWarn = console.warn;
              const originalLog = console.log;
              const originalInfo = console.info;
              const originalDebug = console.debug;
              
              console.error = function(...args) {
                if (args.length > 0 && shouldSuppress(String(args[0]))) return;
                originalError.apply(console, args);
              };
              
              console.warn = function(...args) {
                if (args.length > 0 && shouldSuppress(String(args[0]))) return;
                originalWarn.apply(console, args);
              };
              
              console.log = function(...args) {
                if (args.length > 0 && shouldSuppress(String(args[0]))) return;
                originalLog.apply(console, args);
              };
              
              console.info = function(...args) {
                if (args.length > 0 && shouldSuppress(String(args[0]))) return;
                originalInfo.apply(console, args);
              };
              
              console.debug = function(...args) {
                if (args.length > 0 && shouldSuppress(String(args[0]))) return;
                originalDebug.apply(console, args);
              };
              
              // Global error handler with capture phase
              window.addEventListener('error', function(event) {
                if (shouldSuppress(event.message)) {
                  event.preventDefault();
                  event.stopPropagation();
                  event.stopImmediatePropagation();
                  return false;
                }
              }, true);
              
              // Unhandled promise rejection handler
              window.addEventListener('unhandledrejection', function(event) {
                if (event.reason && shouldSuppress(String(event.reason))) {
                  event.preventDefault();
                  event.stopPropagation();
                  event.stopImmediatePropagation();
                  return false;
                }
              }, true);
              
              // Override fetch to prevent extension-related network errors
              const originalFetch = window.fetch;
              window.fetch = function(...args) {
                const url = String(args[0]);
                if (shouldSuppress(url)) {
                  return Promise.reject(new Error('Extension request blocked'));
                }
                return originalFetch.apply(this, args);
              };
              
              // Override XMLHttpRequest
              const originalXHR = window.XMLHttpRequest;
              window.XMLHttpRequest = function() {
                const xhr = new originalXHR();
                const originalOpen = xhr.open;
                xhr.open = function(method, url, ...args) {
                  if (shouldSuppress(String(url))) {
                    throw new Error('Extension request blocked');
                  }
                  return originalOpen.apply(this, [method, url, ...args]);
                };
                return xhr;
              };
              
              // Suppress extension-related DOM errors
              const originalAddEventListener = EventTarget.prototype.addEventListener;
              EventTarget.prototype.addEventListener = function(type, listener, options) {
                if (typeof listener === 'function') {
                  const wrappedListener = function(event) {
                    try {
                      return listener.call(this, event);
                    } catch (error) {
                      if (shouldSuppress(String(error))) {
                        return;
                      }
                      throw error;
                    }
                  };
                  return originalAddEventListener.call(this, type, wrappedListener, options);
                }
                return originalAddEventListener.call(this, type, listener, options);
              };
            })();
          `}
        </Script>
      </head>
      <body className={inter.className}>
        <ThemeProvider attribute="class" defaultTheme="light" enableSystem disableTransitionOnChange>
          {children}
        </ThemeProvider>
      </body>
    </html>
  )
}
