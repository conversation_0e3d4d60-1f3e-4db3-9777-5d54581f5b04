"use client"

import { Card, CardContent } from "@/components/ui/card"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { User, Spark<PERSON> } from "lucide-react"
import { LoadingDots } from "@/components/loading-dots"
import type { Message } from "@ai-sdk/react"

interface MessageBubbleProps {
  message: Message
  isLoading?: boolean
}

export function MessageBubble({ message, isLoading = false }: MessageBubbleProps) {
  const isUser = message.role === "user"

  return (
    <div className={`flex gap-4 ${isUser ? "flex-row-reverse" : "flex-row"} animate-slide-up group`}>
      <Avatar
        className={`w-10 h-10 shadow-lg ${
          isUser
            ? "bg-gradient-to-br from-blue-500 to-blue-600"
            : "bg-gradient-to-br from-purple-500 via-purple-600 to-blue-600"
        } ring-2 ring-white dark:ring-gray-800`}
      >
        <AvatarFallback className="text-white">
          {isUser ? <User className="w-5 h-5" /> : <Sparkles className="w-5 h-5" />}
        </AvatarFallback>
      </Avatar>

      <Card
        className={`max-w-[85%] transition-all duration-200 ${
          isUser
            ? "bg-gradient-to-br from-blue-500 to-blue-600 text-white border-0 shadow-lg"
            : "bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 shadow-lg hover:shadow-xl"
        } group-hover:scale-[1.02]`}
      >
        <CardContent className="p-4">
          {isLoading ? (
            <div className="flex items-center gap-2">
              <LoadingDots />
              <span className="text-sm text-gray-500 dark:text-gray-400">ChatNova is thinking...</span>
            </div>
          ) : (
            <div className="prose prose-sm max-w-none">
              <div
                className={`whitespace-pre-wrap leading-relaxed ${
                  isUser ? "text-white" : "text-gray-800 dark:text-gray-200"
                }`}
              >
                {message.content}
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
