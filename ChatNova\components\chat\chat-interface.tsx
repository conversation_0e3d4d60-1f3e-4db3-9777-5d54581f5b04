'use client'

import { useState, useRef, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useChatStore } from '@/stores/chat-store'
import { ChatMessages } from './chat-messages'
import { ChatInput } from './chat-input'
import { PDFUpload } from '../pdf/pdf-upload'
import { ChatAvatar } from '../avatar/chat-avatar'
import { Button } from '@/components/ui/button'
import { FileText, X } from 'lucide-react'

export function ChatInterface() {
  const { 
    messages, 
    isLoading, 
    isTyping, 
    uploadedFiles, 
    removeUploadedFile,
    personality 
  } = useChatStore()
  
  const [showPDFUpload, setShowPDFUpload] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages, isTyping])

  return (
    <div className="flex flex-col h-full relative">
      {/* Chat Messages Area */}
      <div className="flex-1 overflow-hidden relative">
        {messages.length === 0 ? (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="flex flex-col items-center justify-center h-full p-8 text-center"
          >
            <ChatAvatar size="large" className="mb-6" />
            <h2 className="text-2xl font-bold mb-4 gradient-text">
              Hello! I'm {personality.name}
            </h2>
            <p className="text-muted-foreground mb-8 max-w-md">
              {personality.description}. How can I help you today?
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-w-2xl">
              <motion.div
                whileHover={{ scale: 1.02 }}
                className="glass-effect rounded-lg p-4 cursor-pointer hover:bg-white/10 transition-all"
              >
                <h3 className="font-semibold mb-2">💬 Ask me anything</h3>
                <p className="text-sm text-muted-foreground">
                  Start a conversation about any topic
                </p>
              </motion.div>
              
              <motion.div
                whileHover={{ scale: 1.02 }}
                className="glass-effect rounded-lg p-4 cursor-pointer hover:bg-white/10 transition-all"
                onClick={() => setShowPDFUpload(true)}
              >
                <h3 className="font-semibold mb-2">📄 Upload PDF</h3>
                <p className="text-sm text-muted-foreground">
                  Extract and analyze document content
                </p>
              </motion.div>
            </div>
          </motion.div>
        ) : (
          <ChatMessages />
        )}
        
        <div ref={messagesEndRef} />
      </div>

      {/* Uploaded Files Display */}
      <AnimatePresence>
        {uploadedFiles.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 20 }}
            className="px-4 py-2 border-t border-border/50"
          >
            <div className="flex flex-wrap gap-2">
              {uploadedFiles.map((file, index) => (
                <motion.div
                  key={`${file.name}-${index}`}
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.8 }}
                  className="flex items-center space-x-2 bg-muted/50 rounded-lg px-3 py-2"
                >
                  <FileText className="w-4 h-4 text-blue-500" />
                  <span className="text-sm font-medium truncate max-w-32">
                    {file.name}
                  </span>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => removeUploadedFile(file.name)}
                    className="h-5 w-5 hover:bg-red-500/20 hover:text-red-500"
                  >
                    <X className="w-3 h-3" />
                  </Button>
                </motion.div>
              ))}
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Chat Input */}
      <div className="border-t border-border/50 bg-background/80 backdrop-blur-sm">
        <ChatInput onPDFUpload={() => setShowPDFUpload(true)} />
      </div>

      {/* PDF Upload Modal */}
      <AnimatePresence>
        {showPDFUpload && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
            onClick={() => setShowPDFUpload(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-background rounded-xl shadow-2xl max-w-md w-full"
              onClick={(e) => e.stopPropagation()}
            >
              <PDFUpload onClose={() => setShowPDFUpload(false)} />
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}
