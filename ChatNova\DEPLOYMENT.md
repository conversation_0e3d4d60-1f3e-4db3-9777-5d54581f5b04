# ChatNova Deployment Guide

This guide covers deploying ChatNova to various platforms.

## Vercel (Recommended)

Vercel is the easiest way to deploy Next.js applications.

### Quick Deploy

1. **Push to GitHub**
   ```bash
   git init
   git add .
   git commit -m "Initial commit"
   git remote add origin https://github.com/yourusername/chatnova.git
   git push -u origin main
   ```

2. **Deploy to Vercel**
   - Go to [vercel.com](https://vercel.com)
   - Sign in with GitHub
   - Click "New Project"
   - Import your ChatNova repository
   - Add environment variables:
     - `GEMINI_API_KEY`: Your Google Gemini API key
   - Click "Deploy"

### Manual Deployment

1. **Install Vercel CLI**
   ```bash
   npm i -g vercel
   ```

2. **Deploy**
   ```bash
   vercel
   ```

3. **Set Environment Variables**
   ```bash
   vercel env add GEMINI_API_KEY
   ```

## Netlify

1. **Build the application**
   ```bash
   npm run build
   npm run export
   ```

2. **Deploy to Netlify**
   - Go to [netlify.com](https://netlify.com)
   - Drag and drop the `out` folder
   - Or connect your GitHub repository

3. **Environment Variables**
   - Go to Site Settings > Environment Variables
   - Add `GEMINI_API_KEY`

## Docker

### Dockerfile

```dockerfile
FROM node:18-alpine AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app
COPY package.json package-lock.json ./
RUN npm ci --only=production

FROM node:18-alpine AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .
RUN npm run build

FROM node:18-alpine AS runner
WORKDIR /app
ENV NODE_ENV production
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs
COPY --from=builder /app/public ./public
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static
USER nextjs
EXPOSE 3000
ENV PORT 3000
CMD ["node", "server.js"]
```

### Build and Run

```bash
docker build -t chatnova .
docker run -p 3000:3000 -e GEMINI_API_KEY=your_key chatnova
```

## Railway

1. **Connect Repository**
   - Go to [railway.app](https://railway.app)
   - Create new project from GitHub repo

2. **Environment Variables**
   - Add `GEMINI_API_KEY` in the Variables tab

3. **Deploy**
   - Railway will automatically deploy on push

## Heroku

1. **Create Heroku App**
   ```bash
   heroku create chatnova-app
   ```

2. **Set Environment Variables**
   ```bash
   heroku config:set GEMINI_API_KEY=your_key
   ```

3. **Deploy**
   ```bash
   git push heroku main
   ```

## Environment Variables

For all deployments, ensure these environment variables are set:

### Required
- `GEMINI_API_KEY`: Your Google Gemini API key

### Optional
- `NEXT_PUBLIC_APP_NAME`: App name (default: ChatNova)
- `NEXT_PUBLIC_APP_VERSION`: App version (default: 1.0.0)

## Performance Optimization

### For Production

1. **Enable compression**
   ```javascript
   // next.config.js
   module.exports = {
     compress: true,
     // ... other config
   }
   ```

2. **Optimize images**
   - Use Next.js Image component
   - Enable image optimization in next.config.js

3. **Enable caching**
   - Configure CDN caching headers
   - Use service workers for offline support

### Monitoring

1. **Vercel Analytics**
   ```bash
   npm install @vercel/analytics
   ```

2. **Error Tracking**
   - Integrate Sentry or similar service
   - Monitor API response times

## Security Considerations

1. **API Key Security**
   - Never expose API keys in client-side code
   - Use environment variables only
   - Rotate keys regularly

2. **CORS Configuration**
   - Configure proper CORS headers
   - Restrict origins in production

3. **Rate Limiting**
   - Implement rate limiting for API routes
   - Monitor usage patterns

## Custom Domain

### Vercel
1. Go to Project Settings > Domains
2. Add your custom domain
3. Configure DNS records as shown

### Netlify
1. Go to Site Settings > Domain Management
2. Add custom domain
3. Update DNS records

## SSL/HTTPS

All recommended platforms provide automatic SSL certificates. Ensure:
- HTTPS is enforced
- HTTP redirects to HTTPS
- Voice features require HTTPS

## Backup and Recovery

1. **Database Backups**
   - If using external database, set up automated backups
   - Export user data regularly

2. **Code Backups**
   - Keep code in version control (Git)
   - Tag releases for easy rollback

## Scaling

### Horizontal Scaling
- Use serverless functions for API routes
- Implement caching strategies
- Consider CDN for static assets

### Vertical Scaling
- Monitor memory usage
- Optimize bundle size
- Use code splitting

## Troubleshooting Deployment

### Common Issues

1. **Build Failures**
   - Check Node.js version compatibility
   - Verify all dependencies are listed
   - Review build logs for specific errors

2. **Runtime Errors**
   - Check environment variables
   - Verify API key permissions
   - Monitor server logs

3. **Performance Issues**
   - Enable compression
   - Optimize images and assets
   - Use performance monitoring tools

### Debug Commands

```bash
# Check build locally
npm run build

# Analyze bundle size
npm run analyze

# Check for security vulnerabilities
npm audit

# Test production build locally
npm run start
```

## Post-Deployment Checklist

- [ ] Verify all features work correctly
- [ ] Test PDF upload functionality
- [ ] Confirm voice features work (HTTPS required)
- [ ] Check mobile responsiveness
- [ ] Verify dark/light mode toggle
- [ ] Test API rate limits
- [ ] Monitor error rates
- [ ] Set up analytics
- [ ] Configure monitoring alerts

## Support

For deployment issues:
1. Check platform-specific documentation
2. Review deployment logs
3. Test locally first
4. Contact platform support if needed
