'use client'

import { motion } from 'framer-motion'
import { useChatStore } from '@/stores/chat-store'
import { cn } from '@/lib/utils'

interface ChatAvatarProps {
  size?: 'small' | 'medium' | 'large'
  isTyping?: boolean
  className?: string
}

export function ChatAvatar({ size = 'medium', isTyping = false, className }: ChatAvatarProps) {
  const { personality } = useChatStore()

  const sizeClasses = {
    small: 'w-8 h-8 text-sm',
    medium: 'w-12 h-12 text-lg',
    large: 'w-20 h-20 text-3xl'
  }

  const glowSize = {
    small: '0 0 10px rgba(59, 130, 246, 0.3)',
    medium: '0 0 15px rgba(59, 130, 246, 0.3)',
    large: '0 0 25px rgba(59, 130, 246, 0.4)'
  }

  return (
    <div className={cn("relative", className)}>
      {/* Main Avatar */}
      <motion.div
        className={cn(
          "relative rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center text-white font-semibold shadow-lg",
          sizeClasses[size]
        )}
        style={{
          boxShadow: glowSize[size]
        }}
        animate={isTyping ? {
          scale: [1, 1.05, 1],
          boxShadow: [
            glowSize[size],
            `0 0 ${size === 'large' ? '30px' : size === 'medium' ? '20px' : '15px'} rgba(59, 130, 246, 0.6)`,
            glowSize[size]
          ]
        } : {
          scale: 1,
          boxShadow: glowSize[size]
        }}
        transition={{
          duration: isTyping ? 2 : 0.3,
          repeat: isTyping ? Infinity : 0,
          ease: "easeInOut"
        }}
        whileHover={{
          scale: 1.1,
          boxShadow: `0 0 ${size === 'large' ? '35px' : size === 'medium' ? '25px' : '20px'} rgba(59, 130, 246, 0.5)`
        }}
      >
        {/* Avatar Content */}
        <motion.span
          animate={isTyping ? {
            rotate: [0, 5, -5, 0]
          } : {}}
          transition={{
            duration: 0.5,
            repeat: isTyping ? Infinity : 0,
            ease: "easeInOut"
          }}
        >
          {personality.avatar}
        </motion.span>

        {/* Pulse Ring for Large Avatar */}
        {size === 'large' && (
          <>
            <motion.div
              className="absolute inset-0 rounded-full border-2 border-blue-400/30"
              animate={{
                scale: [1, 1.2, 1],
                opacity: [0.7, 0.3, 0.7]
              }}
              transition={{
                duration: 3,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            />
            <motion.div
              className="absolute inset-0 rounded-full border-2 border-purple-400/30"
              animate={{
                scale: [1, 1.3, 1],
                opacity: [0.5, 0.2, 0.5]
              }}
              transition={{
                duration: 4,
                repeat: Infinity,
                ease: "easeInOut",
                delay: 0.5
              }}
            />
          </>
        )}
      </motion.div>

      {/* Status Indicator */}
      <motion.div
        className={cn(
          "absolute bottom-0 right-0 rounded-full bg-green-500 border-2 border-background",
          size === 'small' ? 'w-3 h-3' : size === 'medium' ? 'w-4 h-4' : 'w-6 h-6'
        )}
        animate={{
          scale: [1, 1.2, 1],
          opacity: [1, 0.7, 1]
        }}
        transition={{
          duration: 2,
          repeat: Infinity,
          ease: "easeInOut"
        }}
        title="Online"
      />

      {/* Floating Particles for Large Avatar */}
      {size === 'large' && (
        <div className="absolute inset-0 pointer-events-none">
          {[...Array(6)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-1 h-1 bg-blue-400 rounded-full opacity-60"
              style={{
                left: `${20 + Math.random() * 60}%`,
                top: `${20 + Math.random() * 60}%`,
              }}
              animate={{
                y: [0, -20, 0],
                opacity: [0.6, 1, 0.6],
                scale: [1, 1.5, 1],
              }}
              transition={{
                duration: 3 + Math.random() * 2,
                repeat: Infinity,
                delay: Math.random() * 2,
                ease: "easeInOut",
              }}
            />
          ))}
        </div>
      )}

      {/* Thinking Animation for Typing */}
      {isTyping && (
        <motion.div
          className="absolute -top-2 -right-2 w-4 h-4 bg-yellow-400 rounded-full flex items-center justify-center"
          animate={{
            scale: [0, 1, 0],
            rotate: [0, 180, 360]
          }}
          transition={{
            duration: 1.5,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        >
          <span className="text-xs">💭</span>
        </motion.div>
      )}
    </div>
  )
}
