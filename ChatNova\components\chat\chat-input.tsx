'use client'

import { useState, useRef, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Button } from '@/components/ui/button'
import { 
  Send, 
  Mic, 
  MicOff, 
  Paperclip, 
  Smile,
  Square,
  Loader2
} from 'lucide-react'
import { useChatStore } from '@/stores/chat-store'
// Remove the direct import since we'll use the API route
import { useToast } from '@/hooks/use-toast'

interface ChatInputProps {
  onPDFUpload: () => void
}

export function ChatInput({ onPDFUpload }: ChatInputProps) {
  const [input, setInput] = useState('')
  const [isRecording, setIsRecording] = useState(false)
  const [recognition, setRecognition] = useState<SpeechRecognition | null>(null)
  const textareaRef = useRef<HTMLTextAreaElement>(null)
  const { toast } = useToast()

  const { 
    addMessage, 
    setLoading, 
    setTyping, 
    isLoading,
    voiceEnabled,
    uploadedFiles,
    extractedContent
  } = useChatStore()

  // Initialize speech recognition
  useEffect(() => {
    if (typeof window !== 'undefined' && 'webkitSpeechRecognition' in window) {
      const SpeechRecognition = window.webkitSpeechRecognition || window.SpeechRecognition
      const recognitionInstance = new SpeechRecognition()
      
      recognitionInstance.continuous = false
      recognitionInstance.interimResults = false
      recognitionInstance.lang = 'en-US'

      recognitionInstance.onresult = (event) => {
        const transcript = event.results[0][0].transcript
        setInput(prev => prev + transcript)
        setIsRecording(false)
      }

      recognitionInstance.onerror = (event) => {
        console.error('Speech recognition error:', event.error)
        setIsRecording(false)
        toast({
          title: "Voice input error",
          description: "Please try again or check your microphone permissions.",
          variant: "destructive",
        })
      }

      recognitionInstance.onend = () => {
        setIsRecording(false)
      }

      setRecognition(recognitionInstance)
    }
  }, [toast])

  // Auto-resize textarea
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto'
      textareaRef.current.style.height = `${Math.min(textareaRef.current.scrollHeight, 120)}px`
    }
  }, [input])

  const handleSubmit = async (e?: React.FormEvent) => {
    e?.preventDefault()
    
    if (!input.trim() && uploadedFiles.length === 0) return
    
    const messageContent = input.trim()
    const hasFiles = uploadedFiles.length > 0
    const context = extractedContent.join('\n\n')

    // Add user message
    addMessage({
      content: messageContent || 'Analyze the uploaded document(s)',
      role: 'user',
      type: hasFiles ? 'pdf' : 'text',
      metadata: hasFiles ? {
        fileName: uploadedFiles.map(f => f.name).join(', '),
        fileSize: uploadedFiles.reduce((acc, f) => acc + f.size, 0)
      } : undefined
    })

    setInput('')
    setLoading(true)
    setTyping(true)

    try {
      // Call the API route
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: messageContent,
          context: context,
          type: hasFiles ? 'pdf' : 'text'
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to get response from AI')
      }

      const data = await response.json()

      // Simulate typing delay for better UX
      await new Promise(resolve => setTimeout(resolve, 1000))

      addMessage({
        content: data.response,
        role: 'assistant',
        type: hasFiles ? 'pdf' : 'text',
        metadata: hasFiles ? {
          processingTime: Math.floor(Math.random() * 2000) + 500
        } : undefined
      })
    } catch (error) {
      console.error('Error sending message:', error)
      toast({
        title: "Error",
        description: "Failed to send message. Please try again.",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
      setTyping(false)
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSubmit()
    }
  }

  const toggleRecording = () => {
    if (!recognition) {
      toast({
        title: "Voice input not supported",
        description: "Your browser doesn't support voice input.",
        variant: "destructive",
      })
      return
    }

    if (isRecording) {
      recognition.stop()
      setIsRecording(false)
    } else {
      recognition.start()
      setIsRecording(true)
    }
  }

  const canSend = (input.trim() || uploadedFiles.length > 0) && !isLoading

  return (
    <div className="p-4">
      <form onSubmit={handleSubmit} className="relative">
        <div className="flex items-end space-x-2">
          {/* Attachment Button */}
          <Button
            type="button"
            variant="ghost"
            size="icon"
            onClick={onPDFUpload}
            className="flex-shrink-0 hover:bg-white/10"
            title="Upload PDF"
          >
            <Paperclip className="w-4 h-4" />
          </Button>

          {/* Input Container */}
          <div className="flex-1 relative">
            <textarea
              ref={textareaRef}
              value={input}
              onChange={(e) => setInput(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder={
                uploadedFiles.length > 0 
                  ? "Ask about your document or press Enter to analyze..."
                  : "Type your message..."
              }
              className="w-full resize-none rounded-2xl border border-input bg-background/50 backdrop-blur-sm px-4 py-3 pr-12 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 scrollbar-thin"
              rows={1}
              disabled={isLoading}
            />

            {/* Voice Recording Indicator */}
            <AnimatePresence>
              {isRecording && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.8 }}
                  className="absolute inset-0 rounded-2xl bg-red-500/10 border-2 border-red-500/30 flex items-center justify-center"
                >
                  <div className="flex items-center space-x-2 text-red-500">
                    <motion.div
                      animate={{ scale: [1, 1.2, 1] }}
                      transition={{ duration: 1, repeat: Infinity }}
                    >
                      <Mic className="w-4 h-4" />
                    </motion.div>
                    <span className="text-sm font-medium">Listening...</span>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>

          {/* Voice Button */}
          {voiceEnabled && (
            <Button
              type="button"
              variant="ghost"
              size="icon"
              onClick={toggleRecording}
              className={`flex-shrink-0 hover:bg-white/10 ${
                isRecording ? 'text-red-500 bg-red-500/10' : ''
              }`}
              title={isRecording ? "Stop recording" : "Start voice input"}
              disabled={isLoading}
            >
              {isRecording ? (
                <Square className="w-4 h-4" />
              ) : (
                <Mic className="w-4 h-4" />
              )}
            </Button>
          )}

          {/* Send Button */}
          <Button
            type="submit"
            variant="gradient"
            size="icon"
            disabled={!canSend}
            className="flex-shrink-0"
            title="Send message"
          >
            {isLoading ? (
              <Loader2 className="w-4 h-4 animate-spin" />
            ) : (
              <Send className="w-4 h-4" />
            )}
          </Button>
        </div>

        {/* Input Footer */}
        <div className="flex items-center justify-between mt-2 text-xs text-muted-foreground">
          <div className="flex items-center space-x-4">
            {uploadedFiles.length > 0 && (
              <span>{uploadedFiles.length} file(s) attached</span>
            )}
            {voiceEnabled && (
              <span className="flex items-center space-x-1">
                <Mic className="w-3 h-3" />
                <span>Voice enabled</span>
              </span>
            )}
          </div>
          
          <div className="flex items-center space-x-2">
            <span>Press Enter to send, Shift+Enter for new line</span>
          </div>
        </div>
      </form>
    </div>
  )
}
